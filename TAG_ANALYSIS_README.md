# 🏷️ 网页标签自动识别系统

## 概述

本系统为Chrome插件提供了强大的网页标签自动识别功能，通过多种算法的融合，能够智能分析网页内容并生成最贴合的标签。支持中英文内容，无需外部API，完全在浏览器端运行。

## 🎯 核心特性

### 1. 多算法融合
- **URL模式匹配**: 基于域名快速识别网站类型
- **TF-IDF分析**: 统计分析提取关键词
- **语义分析**: 基于预定义规则的智能分类
- **内容分析**: 分析页面标题、描述和主要内容

### 2. 智能分类体系
- 🔧 **技术开发**: 编程、开发、代码、框架等
- 🎨 **设计创意**: UI/UX、视觉设计、创意等
- 💼 **商业财经**: 企业、产品、营销、电商等
- 📚 **教育学习**: 课程、培训、知识、技能等
- 📰 **新闻资讯**: 新闻、媒体、时事、报道等
- 🎮 **娱乐休闲**: 游戏、影视、音乐、体育等
- 🏠 **生活方式**: 健康、美食、旅游、时尚等
- 🔬 **科学技术**: 研究、创新、AI、数据科学等

### 3. 置信度评估
- **高置信度** (≥0.7): 算法高度确信的标签
- **中等置信度** (0.4-0.7): 较为可能的标签
- **低置信度** (<0.4): 可能相关的标签

## 🚀 快速开始

### 基础使用

```javascript
// 1. 基础标签分析器
const analyzer = new TagAnalyzer();
const pageData = {
    title: document.title,
    url: window.location.href,
    description: getMetaContent("description"),
    keywords: getMetaContent("keywords")
};

const tags = analyzer.analyzeTags(pageData);
console.log('生成的标签:', tags);
```

### 高级使用

```javascript
// 2. 高级标签分析器（多算法融合）
const advancedAnalyzer = new AdvancedTagAnalyzer();
const tags = await advancedAnalyzer.analyze(pageData);

// 结果格式
tags.forEach(tagInfo => {
    console.log(`标签: ${tagInfo.tag}`);
    console.log(`分数: ${tagInfo.score}`);
    console.log(`置信度: ${tagInfo.confidence}`);
    console.log(`来源算法: ${tagInfo.sources.join(', ')}`);
});
```

## 📁 文件结构

```
scripts/
├── tagAnalyzer.js          # 基础标签分析器
├── advancedTagAnalyzer.js  # 高级多算法分析器
├── tagConfig.js            # 配置文件和规则定义
└── content.js              # 集成到内容脚本
```

## ⚙️ 配置说明

### 算法权重配置

```javascript
// 在 tagConfig.js 中调整算法权重
algorithmWeights: {
    url: 0.25,        // URL模式匹配权重
    meta: 0.20,       // Meta关键词权重
    title: 0.20,      // 标题分析权重
    description: 0.15, // 描述分析权重
    content: 0.20     // 页面内容权重
}
```

### 添加新的URL模式

```javascript
// 在 tagConfig.js 中添加新的URL匹配规则
urlPatterns: {
    'example.com': { 
        tags: ['示例', '测试', '演示'], 
        confidence: 0.9, 
        category: 'technology' 
    }
}
```

### 扩展标签分类

```javascript
// 添加新的标签分类
categories: {
    newCategory: {
        name: '新分类',
        keywords: ['关键词1', '关键词2'],
        tags: ['标签1', '标签2'],
        weight: 1.0,
        color: '#FF5722'
    }
}
```

## 🔧 算法详解

### 1. URL模式匹配算法
- **原理**: 基于域名和路径特征快速分类
- **优势**: 速度快，准确率高
- **适用**: 知名网站和平台

```javascript
// 示例：GitHub网站自动识别为技术类
'github.com': { 
    tags: ['技术', '开源', '代码'], 
    confidence: 0.95 
}
```

### 2. TF-IDF关键词提取
- **原理**: 计算词频和逆文档频率
- **优势**: 能识别页面最重要的词汇
- **适用**: 内容丰富的页面

### 3. 规则基础分类器
- **原理**: 基于预定义规则和关键词匹配
- **优势**: 可控性强，易于调整
- **适用**: 特定领域的精确分类

### 4. 语义相似度分析
- **原理**: 计算内容与预定义标签的语义相关性
- **优势**: 能处理同义词和相关概念
- **适用**: 语义理解和概念匹配

## 📊 性能优化

### 1. 内容长度限制
```javascript
// 限制分析的文本长度，避免性能问题
const textContent = document.body.textContent.slice(0, 3000);
```

### 2. 停用词过滤
```javascript
// 过滤常见的无意义词汇
const stopWords = new Set(['的', '了', '在', 'the', 'a', 'an']);
```

### 3. 结果缓存
```javascript
// 缓存分析结果，避免重复计算
const cacheKey = `tags_${url}_${titleHash}`;
```

## 🎨 UI集成示例

### 在popup中显示标签

```javascript
// popup.js 中的集成示例
function displayTags(tags) {
    const container = document.getElementById('tags-container');
    container.innerHTML = '';
    
    tags.forEach(tagInfo => {
        const tagElement = document.createElement('span');
        tagElement.className = `tag ${tagInfo.confidence}-confidence`;
        tagElement.textContent = tagInfo.tag;
        tagElement.title = `分数: ${tagInfo.score}, 置信度: ${tagInfo.confidence}`;
        container.appendChild(tagElement);
    });
}
```

### CSS样式示例

```css
.tag {
    display: inline-block;
    padding: 4px 12px;
    margin: 2px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
}

.tag.high-confidence {
    background: #4CAF50;
    color: white;
}

.tag.medium-confidence {
    background: #FF9800;
    color: white;
}

.tag.low-confidence {
    background: #9E9E9E;
    color: white;
}
```

## 🔍 测试和调试

### 1. 测试不同类型的网页
- 技术博客 (如 CSDN、掘金)
- 电商网站 (如 淘宝、京东)
- 新闻网站 (如 新华网、人民网)
- 视频网站 (如 B站、YouTube)

### 2. 调试输出
```javascript
// 启用详细日志
console.log('页面数据:', pageData);
console.log('算法结果:', algorithmResults);
console.log('最终标签:', finalTags);
```

### 3. 性能监控
```javascript
// 监控分析耗时
const startTime = performance.now();
const tags = await analyzer.analyze(pageData);
const endTime = performance.now();
console.log(`分析耗时: ${endTime - startTime}ms`);
```

## 🚀 未来扩展

### 1. 机器学习集成
- 集成轻量级的机器学习模型
- 支持用户反馈学习
- 个性化标签推荐

### 2. 多语言支持
- 扩展更多语言的分词支持
- 多语言标签体系
- 跨语言语义理解

### 3. 实时更新
- 标签规则的在线更新
- 新兴概念的自动识别
- 社区贡献的标签库

## 📝 注意事项

1. **性能考虑**: 避免在大型页面上进行过度分析
2. **隐私保护**: 所有分析都在本地进行，不发送数据到外部服务器
3. **准确性**: 算法结果仅供参考，建议结合人工审核
4. **兼容性**: 确保在不同浏览器环境下的兼容性

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进标签识别算法：

1. 报告算法识别错误的案例
2. 建议新的标签分类
3. 提供更好的关键词规则
4. 优化算法性能

---

通过这套智能标签识别系统，你的Chrome插件将能够为用户提供准确、智能的网页标签分类功能，大大提升用户体验和内容管理效率。
