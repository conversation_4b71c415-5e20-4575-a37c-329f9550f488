<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="这是一个关于JavaScript React开发的技术博客，分享前端开发经验和最佳实践">
    <meta name="keywords" content="JavaScript, React, 前端开发, 编程, 技术博客, Web开发">
    <meta name="author" content="技术博主">
    <title>React开发实战：构建现代化Web应用的最佳实践 - 技术博客</title>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2196F3, #21CBF3);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.2em;
        }
        
        .content {
            padding: 40px;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #2196F3;
        }
        
        .demo-section h2 {
            color: #2196F3;
            margin-top: 0;
            font-size: 1.8em;
        }
        
        .tag-display {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
        }
        
        .tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9em;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: transform 0.2s ease;
        }
        
        .tag:hover {
            transform: translateY(-2px);
        }
        
        .tag.high-confidence {
            background: linear-gradient(135deg, #4CAF50, #45a049);
        }
        
        .tag.medium-confidence {
            background: linear-gradient(135deg, #FF9800, #F57C00);
        }
        
        .tag.low-confidence {
            background: linear-gradient(135deg, #9E9E9E, #757575);
        }
        
        .test-button {
            background: linear-gradient(135deg, #2196F3, #21CBF3);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
        }
        
        .algorithm-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #e0e0e0;
        }
        
        .algorithm-info h3 {
            color: #2196F3;
            margin-top: 0;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-top: 4px solid #2196F3;
        }
        
        .feature-card h3 {
            color: #2196F3;
            margin-top: 0;
        }
        
        .code-block {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 20px 0;
        }
        
        .highlight {
            background: linear-gradient(135deg, #FFE082, #FFCC02);
            padding: 2px 6px;
            border-radius: 4px;
            color: #333;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏷️ 智能标签分析演示</h1>
            <p>体验多算法融合的网页标签自动识别系统</p>
        </div>
        
        <div class="content">
            <div class="demo-section">
                <h2>🎯 当前页面标签分析</h2>
                <p>点击下方按钮，系统将分析当前页面并生成智能标签：</p>
                <button class="test-button" onclick="analyzeCurrentPage()">🔍 分析当前页面标签</button>
                <div id="current-tags" class="tag-display"></div>
            </div>
            
            <div class="demo-section">
                <h2>🧠 算法特性展示</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>🔗 URL模式识别</h3>
                        <p>基于域名特征快速识别网站类型，如GitHub→技术、淘宝→电商</p>
                        <div class="tag-display">
                            <span class="tag high-confidence">技术</span>
                            <span class="tag high-confidence">开源</span>
                            <span class="tag high-confidence">代码</span>
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <h3>📝 内容语义分析</h3>
                        <p>分析标题、描述和页面内容，提取关键概念和主题</p>
                        <div class="tag-display">
                            <span class="tag medium-confidence">React</span>
                            <span class="tag medium-confidence">前端开发</span>
                            <span class="tag medium-confidence">JavaScript</span>
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <h3>🎯 TF-IDF算法</h3>
                        <p>计算词频和逆文档频率，识别页面最重要的关键词</p>
                        <div class="tag-display">
                            <span class="tag high-confidence">Web开发</span>
                            <span class="tag medium-confidence">最佳实践</span>
                            <span class="tag low-confidence">现代化</span>
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <h3>🔄 多算法融合</h3>
                        <p>结合多种算法结果，通过权重计算得出最终标签</p>
                        <div class="tag-display">
                            <span class="tag high-confidence">技术博客</span>
                            <span class="tag medium-confidence">编程教程</span>
                            <span class="tag medium-confidence">开发经验</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="demo-section">
                <h2>⚙️ 算法配置</h2>
                <div class="algorithm-info">
                    <h3>支持的分析算法</h3>
                    <ul>
                        <li><span class="highlight">URL模式匹配</span> - 基于域名和路径的快速分类</li>
                        <li><span class="highlight">Meta关键词提取</span> - 利用页面元数据信息</li>
                        <li><span class="highlight">标题语义分析</span> - 分析页面标题的语义内容</li>
                        <li><span class="highlight">内容TF-IDF分析</span> - 统计分析页面文本内容</li>
                        <li><span class="highlight">规则基础分类</span> - 基于预定义规则的智能分类</li>
                        <li><span class="highlight">语义相似度计算</span> - 计算内容与标签的语义相关性</li>
                    </ul>
                </div>
                
                <div class="algorithm-info">
                    <h3>标签置信度说明</h3>
                    <div class="tag-display">
                        <span class="tag high-confidence">高置信度 (≥0.7)</span>
                        <span class="tag medium-confidence">中等置信度 (0.4-0.7)</span>
                        <span class="tag low-confidence">低置信度 (<0.4)</span>
                    </div>
                </div>
            </div>
            
            <div class="demo-section">
                <h2>📊 技术实现</h2>
                <p>本系统采用纯JavaScript实现，无需外部API，支持中英文内容分析：</p>
                <div class="code-block">
// 示例：标签分析核心代码
const analyzer = new AdvancedTagAnalyzer();
const pageData = {
    title: document.title,
    url: window.location.href,
    description: getMetaContent("description"),
    keywords: getMetaContent("keywords")
};

const tags = await analyzer.analyze(pageData);
console.log('生成的标签:', tags);
                </div>
            </div>
        </div>
    </div>
    
    <script>
        async function analyzeCurrentPage() {
            const button = document.querySelector('.test-button');
            const tagsContainer = document.getElementById('current-tags');
            
            button.textContent = '🔄 分析中...';
            button.disabled = true;
            
            try {
                // 模拟标签分析（实际使用时会调用真实的分析器）
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                const mockTags = [
                    { tag: '技术', score: 0.95, confidence: 'high' },
                    { tag: 'JavaScript', score: 0.88, confidence: 'high' },
                    { tag: 'React', score: 0.82, confidence: 'high' },
                    { tag: '前端开发', score: 0.75, confidence: 'high' },
                    { tag: 'Web开发', score: 0.68, confidence: 'medium' },
                    { tag: '编程', score: 0.62, confidence: 'medium' },
                    { tag: '最佳实践', score: 0.55, confidence: 'medium' },
                    { tag: '技术博客', score: 0.48, confidence: 'medium' },
                    { tag: '开发经验', score: 0.35, confidence: 'low' }
                ];
                
                tagsContainer.innerHTML = '';
                mockTags.forEach(tagInfo => {
                    const tagElement = document.createElement('span');
                    tagElement.className = `tag ${tagInfo.confidence}-confidence`;
                    tagElement.textContent = `${tagInfo.tag} (${tagInfo.score})`;
                    tagElement.title = `置信度: ${tagInfo.confidence}, 分数: ${tagInfo.score}`;
                    tagsContainer.appendChild(tagElement);
                });
                
            } catch (error) {
                tagsContainer.innerHTML = '<p style="color: red;">分析失败: ' + error.message + '</p>';
            } finally {
                button.textContent = '🔍 重新分析';
                button.disabled = false;
            }
        }
        
        // 页面加载时自动分析一次
        window.addEventListener('load', () => {
            setTimeout(analyzeCurrentPage, 1000);
        });
    </script>
</body>
</html>
