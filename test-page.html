<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="这是一个关于JavaScript React开发的技术博客，分享前端开发经验和最佳实践，包含Vue.js、Node.js等现代Web开发技术">
    <meta name="keywords" content="JavaScript, React, Vue.js, 前端开发, 编程, 技术博客, Web开发, Node.js, 算法, 数据结构">
    <meta name="author" content="技术博主">
    <title>React + Vue.js 前端开发实战教程 - 现代JavaScript编程技术博客</title>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2196F3, #21CBF3);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .content {
            padding: 40px;
        }
        
        .article {
            margin-bottom: 30px;
        }
        
        .article h2 {
            color: #2196F3;
            border-bottom: 2px solid #2196F3;
            padding-bottom: 10px;
        }
        
        .article h3 {
            color: #666;
            margin-top: 25px;
        }
        
        .code-block {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 20px 0;
        }
        
        .highlight {
            background: linear-gradient(135deg, #FFE082, #FFCC02);
            padding: 2px 6px;
            border-radius: 4px;
            color: #333;
            font-weight: bold;
        }
        
        .tech-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .tech-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 现代前端开发技术栈</h1>
            <p>掌握React、Vue.js、JavaScript等核心技术</p>
        </div>
        
        <div class="content">
            <div class="article">
                <h2>JavaScript 基础与进阶</h2>
                <p>JavaScript是现代Web开发的核心语言，掌握ES6+语法、异步编程、模块化开发是每个前端开发者的必备技能。</p>
                
                <h3>核心概念</h3>
                <ul>
                    <li><span class="highlight">闭包</span>和作用域链</li>
                    <li><span class="highlight">Promise</span>和async/await异步编程</li>
                    <li><span class="highlight">原型链</span>和面向对象编程</li>
                    <li><span class="highlight">模块化</span>：ES6 Modules、CommonJS</li>
                </ul>
                
                <div class="code-block">
// React Hooks 示例
import React, { useState, useEffect } from 'react';

function UserProfile({ userId }) {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);
    
    useEffect(() => {
        fetchUser(userId).then(userData => {
            setUser(userData);
            setLoading(false);
        });
    }, [userId]);
    
    if (loading) return &lt;div&gt;Loading...&lt;/div&gt;;
    
    return (
        &lt;div className="user-profile"&gt;
            &lt;h2&gt;{user.name}&lt;/h2&gt;
            &lt;p&gt;{user.email}&lt;/p&gt;
        &lt;/div&gt;
    );
}
                </div>
            </div>
            
            <div class="article">
                <h2>React 开发实战</h2>
                <p>React是目前最流行的前端框架之一，通过组件化开发、虚拟DOM、状态管理等特性，大大提升了开发效率和用户体验。</p>
                
                <div class="tech-list">
                    <div class="tech-item">
                        <h3>🔧 开发工具</h3>
                        <ul>
                            <li>Create React App</li>
                            <li>Vite</li>
                            <li>Webpack</li>
                            <li>Babel</li>
                        </ul>
                    </div>
                    
                    <div class="tech-item">
                        <h3>📦 状态管理</h3>
                        <ul>
                            <li>Redux Toolkit</li>
                            <li>Zustand</li>
                            <li>Context API</li>
                            <li>React Query</li>
                        </ul>
                    </div>
                    
                    <div class="tech-item">
                        <h3>🎨 UI组件库</h3>
                        <ul>
                            <li>Material-UI</li>
                            <li>Ant Design</li>
                            <li>Chakra UI</li>
                            <li>React Bootstrap</li>
                        </ul>
                    </div>
                    
                    <div class="tech-item">
                        <h3>🧪 测试工具</h3>
                        <ul>
                            <li>Jest</li>
                            <li>React Testing Library</li>
                            <li>Cypress</li>
                            <li>Storybook</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="article">
                <h2>Vue.js 生态系统</h2>
                <p>Vue.js以其简洁的语法和渐进式的设计理念，成为了前端开发的热门选择。Vue 3的Composition API为大型应用开发提供了更好的支持。</p>
                
                <h3>Vue 3 新特性</h3>
                <ul>
                    <li><span class="highlight">Composition API</span> - 更好的逻辑复用</li>
                    <li><span class="highlight">Teleport</span> - 组件传送门</li>
                    <li><span class="highlight">Fragments</span> - 多根节点组件</li>
                    <li><span class="highlight">Suspense</span> - 异步组件处理</li>
                </ul>
                
                <div class="code-block">
// Vue 3 Composition API 示例
import { ref, computed, onMounted } from 'vue'

export default {
    setup() {
        const count = ref(0)
        const doubleCount = computed(() => count.value * 2)
        
        const increment = () => {
            count.value++
        }
        
        onMounted(() => {
            console.log('组件已挂载')
        })
        
        return {
            count,
            doubleCount,
            increment
        }
    }
}
                </div>
            </div>
            
            <div class="article">
                <h2>Node.js 后端开发</h2>
                <p>Node.js让JavaScript能够运行在服务器端，配合Express、Koa等框架，可以快速构建高性能的Web应用和API服务。</p>
                
                <h3>核心技术栈</h3>
                <ul>
                    <li><span class="highlight">Express.js</span> - 轻量级Web框架</li>
                    <li><span class="highlight">MongoDB</span> - NoSQL数据库</li>
                    <li><span class="highlight">JWT</span> - 身份认证</li>
                    <li><span class="highlight">Socket.io</span> - 实时通信</li>
                </ul>
            </div>
            
            <div class="article">
                <h2>算法与数据结构</h2>
                <p>扎实的算法和数据结构基础是优秀程序员的必备素养，对于前端开发者来说，理解常用算法有助于写出更高效的代码。</p>
                
                <h3>常用算法</h3>
                <ul>
                    <li>排序算法：快速排序、归并排序</li>
                    <li>搜索算法：二分查找、深度优先搜索</li>
                    <li>动态规划：背包问题、最长公共子序列</li>
                    <li>图算法：最短路径、拓扑排序</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // 模拟一些交互功能
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成 - 这是一个技术博客页面');
            console.log('主要内容：JavaScript、React、Vue.js、Node.js、算法');
        });
    </script>
</body>
</html>
