<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebVault - 网页信息采集器</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #4361ee;
            --primary-dark: #3a56d4;
            --secondary: #06d6a0;
            --dark: #1e1e2c;
            --darker: #161622;
            --light: #f8f9fa;
            --gray: #6c757d;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Noto Sans SC', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            width: 500px;
            min-height: 600px;
            background: linear-gradient(135deg, var(--darker), var(--dark));
            color: var(--light);
            padding: 20px;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
        }

        /* 头部样式 */
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, var(--primary), #5e35b1);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
        }

        .logo-icon i {
            font-size: 18px;
            color: white;
        }

        .logo h1 {
            font-size: 22px;
            font-weight: 700;
            background: linear-gradient(to right, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: -0.5px;
        }

        .status {
            background: rgba(255, 255, 255, 0.08);
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: var(--success);
        }

        /* 主内容区 */
        .main-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            flex: 1;
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
            color: var(--secondary);
        }

        .info-grid {
            display: grid;
            grid-template-columns: 120px 1fr;
            gap: 15px;
        }

        .info-label {
            font-weight: 600;
            opacity: 0.8;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #a0aec0;
        }

        .info-value {
            word-break: break-word;
            line-height: 1.5;
        }

        /* 截图区域 */
        .screenshot-container {
            position: relative;
            margin-top: 15px;
            border-radius: 12px;
            overflow: hidden;
        }

        .screenshot {
            width: 100%;
            display: block;
            transition: transform 0.3s ease;
        }

        .screenshot:hover {
            transform: scale(1.02);
        }

        .screenshot-placeholder {
            height: 200px;
            background: linear-gradient(45deg, rgba(67, 97, 238, 0.1), rgba(6, 214, 160, 0.1));
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 10px;
            color: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            border: 2px dashed rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .screenshot-placeholder:hover {
            background: linear-gradient(45deg, rgba(67, 97, 238, 0.15), rgba(6, 214, 160, 0.15));
            color: rgba(255, 255, 255, 0.7);
            border-color: var(--primary);
        }

        /* 图标区域 */
        .logo-container {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-top: 10px;
        }

        .logo-preview {
            width: 64px;
            height: 64px;
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid rgba(255, 255, 255, 0.15);
            overflow: hidden;
            flex-shrink: 0;
        }

        .logo-preview img {
            width: 80%;
            height: 80%;
            object-fit: contain;
        }

        /* 操作按钮 */
        .action-buttons {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 12px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            flex: 1;
            box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), #2f44b8);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        /* 通知 */
        .notification {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--success);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
            animation: fadeInOut 3s forwards;
            display: none;
            z-index: 1000;
            font-weight: 500;
        }

        @keyframes fadeInOut {

            0%,
            100% {
                opacity: 0;
                bottom: 0;
            }

            10%,
            90% {
                opacity: 1;
                bottom: 20px;
            }
        }

        /* 元数据区域 */
        .metadata-container {
            max-height: 300px;
            overflow-y: auto;
            padding: 15px;
            background: rgba(0, 0, 0, 0.15);
            border-radius: 12px;
            margin-top: 15px;
            font-family: 'Fira Code', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            line-height: 1.6;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        /* 进度条 */
        .progress-bar {
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            margin-top: 10px;
            overflow: hidden;
        }

        .progress {
            height: 100%;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            width: 0%;
            transition: width 0.3s ease;
        }

        /* 选项卡 */
        .tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .tab {
            padding: 8px 16px;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.08);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
        }

        .tab.active {
            background: var(--primary);
            box-shadow: 0 4px 10px rgba(67, 97, 238, 0.3);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 图标网格 */
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-top: 15px;
        }

        .icon-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .icon-preview {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            overflow: hidden;
            padding: 6px;
        }

        .icon-preview img {
            max-width: 100%;
            max-height: 100%;
        }

        .icon-size {
            font-size: 11px;
            opacity: 0.7;
        }

        /* 页脚 */
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.5);
            padding-top: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* 响应式调整 */
        @media (max-width: 500px) {
            body {
                width: 100%;
                padding: 15px;
            }

            .info-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .icon-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
    </style>
</head>

<body>
    <div class="header">
        <div class="logo">
            <div class="logo-icon">
                <i class="fas fa-box-archive"></i>
            </div>
            <h1>WebVault</h1>
        </div>
        <div class="status">
            <div class="status-dot"></div>
            <span>已连接</span>
        </div>
    </div>

    <div class="main-container">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-info-circle"></i> 基本信息</h2>
                <div class="tags">
                    <span class="tag">HTML5</span>
                    <span class="tag">响应式</span>
                </div>
            </div>

            <div class="info-grid">
                <div class="info-label"><i class="fas fa-heading"></i> 标题:</div>
                <div class="info-value" id="page-title">WebVault - 网页信息采集工具</div>

                <div class="info-label"><i class="fas fa-link"></i> URL:</div>
                <div class="info-value" id="page-url">https://www.webvault.com/dashboard</div>

                <div class="info-label"><i class="fas fa-quote-left"></i> 描述:</div>
                <div class="info-value" id="page-description">WebVault是一款强大的网页信息采集工具，可快速捕获网页元数据、截图和图标，并保存到您的个人资源库。</div>

                <div class="info-label"><i class="fas fa-code"></i> 字符集:</div>
                <div class="info-value" id="page-charset">UTF-8</div>

                <div class="info-label"><i class="fas fa-keyword"></i> 关键词:</div>
                <div class="info-value" id="page-keywords">网页采集, 元数据, 截图工具, 资源管理, WebVault</div>

                <div class="info-label"><i class="fas fa-user-tag"></i> 作者:</div>
                <div class="info-value" id="page-author">WebVault Team</div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-camera"></i> 网页截图</h2>
            </div>

            <div class="screenshot-container">
                <div class="screenshot-placeholder" id="screenshot-placeholder">
                    <i class="fas fa-camera" style="font-size: 40px;"></i>
                    <p>点击生成网页截图</p>
                </div>
                <img src="" alt="网页截图" class="screenshot" id="screenshot" style="display: none;">
            </div>

            <div class="progress-bar">
                <div class="progress" id="screenshot-progress"></div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-icons"></i> 网站图标</h2>
            </div>

            <div class="tabs">
                <div class="tab active" data-tab="favicon">主图标</div>
                <div class="tab" data-tab="apple-touch">Apple Touch</div>
                <div class="tab" data-tab="all-icons">所有图标</div>
            </div>

            <div class="tab-content active" id="favicon-tab">
                <div class="logo-container">
                    <div class="logo-preview">
                        <img src="https://www.google.com/favicon.ico" alt="网站图标" id="favicon-img">
                    </div>
                    <div>
                        <div><strong>主图标URL:</strong> <span id="favicon-url">https://www.webvault.com/favicon.ico</span>
                        </div>
                        <div><strong>尺寸:</strong> <span id="favicon-size">64x64 像素</span></div>
                    </div>
                </div>
            </div>

            <div class="tab-content" id="apple-touch-tab">
                <div class="logo-container">
                    <div class="logo-preview">
                        <img src="https://www.apple.com/apple-touch-icon.png" alt="Apple Touch 图标" id="apple-touch-img">
                    </div>
                    <div>
                        <div><strong>Apple Touch URL:</strong> <span
                                id="apple-touch-url">https://www.webvault.com/apple-touch-icon.png</span></div>
                        <div><strong>尺寸:</strong> <span id="apple-touch-size">180x180 像素</span></div>
                    </div>
                </div>
            </div>

            <div class="tab-content" id="all-icons-tab">
                <div class="icon-grid">
                    <div class="icon-item">
                        <div class="icon-preview">
                            <img src="https://www.google.com/favicon.ico" alt="图标">
                        </div>
                        <div class="icon-size">16x16</div>
                    </div>
                    <div class="icon-item">
                        <div class="icon-preview">
                            <img src="https://www.google.com/favicon.ico" alt="图标">
                        </div>
                        <div class="icon-size">32x32</div>
                    </div>
                    <div class="icon-item">
                        <div class="icon-preview">
                            <img src="https://www.google.com/favicon.ico" alt="图标">
                        </div>
                        <div class="icon-size">48x48</div>
                    </div>
                    <div class="icon-item">
                        <div class="icon-preview">
                            <img src="https://www.apple.com/apple-touch-icon.png" alt="图标">
                        </div>
                        <div class="icon-size">64x64</div>
                    </div>
                    <div class="icon-item">
                        <div class="icon-preview">
                            <img src="https://www.apple.com/apple-touch-icon.png" alt="图标">
                        </div>
                        <div class="icon-size">96x96</div>
                    </div>
                    <div class="icon-item">
                        <div class="icon-preview">
                            <img src="https://www.apple.com/apple-touch-icon.png" alt="图标">
                        </div>
                        <div class="icon-size">128x128</div>
                    </div>
                    <div class="icon-item">
                        <div class="icon-preview">
                            <img src="https://www.apple.com/apple-touch-icon.png" alt="图标">
                        </div>
                        <div class="icon-size">192x192</div>
                    </div>
                    <div class="icon-item">
                        <div class="icon-preview">
                            <img src="https://www.apple.com/apple-touch-icon.png" alt="图标">
                        </div>
                        <div class="icon-size">256x256</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-database"></i> 元数据</h2>
            </div>

            <div class="metadata-container" id="metadata-container">
                {
                "title": "WebVault - 网页信息采集工具",
                "url": "https://www.webvault.com/dashboard",
                "description": "WebVault是一款强大的网页信息采集工具...",
                "charset": "UTF-8",
                "keywords": ["网页采集", "元数据", "截图工具", "资源管理"],
                "author": "WebVault Team",
                "viewport": "width=device-width, initial-scale=1.0",
                "og:title": "WebVault - 一站式网页信息管理解决方案",
                "og:description": "快速捕获网页信息并保存到您的个人资源库...",
                "og:image": "https://www.webvault.com/images/og-image.jpg",
                "twitter:card": "summary_large_image",
                "generator": "Next.js",
                "theme-color": "#4361ee"
                }
            </div>
        </div>

        <div class="action-buttons">
            <button class="btn btn-secondary" id="refresh-btn">
                <i class="fas fa-sync-alt"></i> 重新采集
            </button>
            <button class="btn btn-primary" id="save-btn">
                <i class="fas fa-cloud-upload-alt"></i> 保存到资源库
            </button>
        </div>
    </div>

    <div class="footer">
        WebVault &copy; 2023 - 您的网页信息管理专家
    </div>

    <div class="notification" id="notification">
        数据已成功保存到资源库！
    </div>

    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function () {
            // 选项卡切换功能
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // 移除所有active类
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));

                    // 添加active类到当前选项卡
                    tab.classList.add('active');
                    const tabId = tab.getAttribute('data-tab') + '-tab';
                    document.getElementById(tabId).classList.add('active');
                });
            });

            // 生成截图功能
            const screenshotPlaceholder = document.getElementById('screenshot-placeholder');
            const screenshot = document.getElementById('screenshot');
            const progressBar = document.getElementById('screenshot-progress');

            screenshotPlaceholder.addEventListener('click', generateScreenshot);

            function generateScreenshot() {
                // 显示加载状态
                screenshotPlaceholder.innerHTML = '<div class="loading"></div><p>正在捕获网页截图...</p>';

                // 模拟截图生成过程
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 5;
                    progressBar.style.width = `${progress}%`;

                    if (progress >= 100) {
                        clearInterval(interval);

                        // 显示截图
                        screenshot.src = 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80';
                        screenshot.style.display = 'block';
                        screenshotPlaceholder.style.display = 'none';

                        // 重置进度条
                        setTimeout(() => {
                            progressBar.style.width = '0%';
                        }, 1000);
                    }
                }, 100);
            }

            // 保存按钮功能
            const saveBtn = document.getElementById('save-btn');
            const notification = document.getElementById('notification');

            saveBtn.addEventListener('click', () => {
                // 显示保存中状态
                saveBtn.innerHTML = '<div class="loading"></div> 保存中...';
                saveBtn.disabled = true;

                // 模拟保存过程
                setTimeout(() => {
                    // 显示通知
                    notification.textContent = "数据已成功保存到资源库！";
                    notification.style.display = 'block';

                    // 重置按钮
                    saveBtn.innerHTML = '<i class="fas fa-cloud-upload-alt"></i> 保存到资源库';
                    saveBtn.disabled = false;

                    // 3秒后隐藏通知
                    setTimeout(() => {
                        notification.style.display = 'none';
                    }, 3000);
                }, 1500);
            });

            // 重新采集按钮功能
            const refreshBtn = document.getElementById('refresh-btn');

            refreshBtn.addEventListener('click', () => {
                refreshBtn.innerHTML = '<div class="loading"></div> 采集数据...';
                refreshBtn.disabled = true;

                setTimeout(() => {
                    // 模拟数据刷新
                    document.getElementById('page-title').textContent =
                        "更新后的标题 - " + new Date().toLocaleTimeString();

                    // 重置按钮
                    refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> 重新采集';
                    refreshBtn.disabled = false;

                    // 显示截图占位符
                    screenshot.style.display = 'none';
                    screenshotPlaceholder.style.display = 'flex';
                    screenshotPlaceholder.innerHTML = '<i class="fas fa-camera" style="font-size: 40px;"></i><p>点击生成新截图</p>';
                }, 1000);
            });
        });
    </script>
</body>

</html>