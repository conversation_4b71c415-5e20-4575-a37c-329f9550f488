<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="这是一个关于JavaScript React开发的技术博客，分享前端开发经验和最佳实践">
    <meta name="keywords" content="JavaScript, React, 前端开发, 编程, 技术博客, Web开发">
    <meta name="author" content="技术博主">
    <title>React开发实战：构建现代化Web应用的最佳实践 - 技术博客</title>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .debug-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .debug-section h3 {
            margin-top: 0;
            color: #007bff;
        }
        
        .tag-result {
            display: inline-block;
            margin: 4px;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .tag-high { background: #28a745; color: white; }
        .tag-medium { background: #ffc107; color: #333; }
        .tag-low { background: #6c757d; color: white; }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .code-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #f5c6cb;
        }
        
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏷️ 标签分析调试工具</h1>
        <p>这个页面用于测试WebVault插件的智能标签分析功能</p>
        
        <div class="debug-section">
            <h3>📄 页面信息</h3>
            <p><strong>标题:</strong> <span id="page-title">-</span></p>
            <p><strong>URL:</strong> <span id="page-url">-</span></p>
            <p><strong>描述:</strong> <span id="page-description">-</span></p>
            <p><strong>关键词:</strong> <span id="page-keywords">-</span></p>
        </div>
        
        <div class="debug-section">
            <h3>🧪 测试控制</h3>
            <button class="test-button" onclick="testBasicTags()">测试基础标签</button>
            <button class="test-button" onclick="testTagAnalyzer()">测试标签分析器</button>
            <button class="test-button" onclick="testPageData()">测试页面数据</button>
            <button class="test-button" onclick="clearResults()">清空结果</button>
        </div>
        
        <div class="debug-section">
            <h3>🏷️ 标签分析结果</h3>
            <div id="tag-results">点击上方按钮开始测试...</div>
        </div>
        
        <div class="debug-section">
            <h3>📊 详细输出</h3>
            <div id="debug-output" class="code-output">等待测试结果...</div>
        </div>
        
        <div class="debug-section">
            <h3>🔧 脚本状态</h3>
            <div id="script-status">检查中...</div>
        </div>
    </div>
    
    <!-- 加载标签分析脚本 -->
    <script src="scripts/tagConfig.js"></script>
    <script src="scripts/tagAnalyzer.js"></script>
    
    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeDebugger();
        });
        
        function initializeDebugger() {
            // 显示页面信息
            document.getElementById('page-title').textContent = document.title;
            document.getElementById('page-url').textContent = window.location.href;
            document.getElementById('page-description').textContent = getMetaContent('description') || '无';
            document.getElementById('page-keywords').textContent = getMetaContent('keywords') || '无';
            
            // 检查脚本状态
            checkScriptStatus();
        }
        
        function checkScriptStatus() {
            const statusDiv = document.getElementById('script-status');
            let status = [];
            
            if (typeof window.TagConfig !== 'undefined') {
                status.push('<span class="success">✅ TagConfig 已加载</span>');
            } else {
                status.push('<span class="error">❌ TagConfig 未加载</span>');
            }
            
            if (typeof window.TagAnalyzer !== 'undefined') {
                status.push('<span class="success">✅ TagAnalyzer 已加载</span>');
            } else {
                status.push('<span class="error">❌ TagAnalyzer 未加载</span>');
            }
            
            statusDiv.innerHTML = status.join('<br>');
        }
        
        function getMetaContent(name) {
            const meta = document.querySelector(`meta[name="${name}"], meta[property="${name}"]`);
            return meta ? meta.getAttribute('content') || '' : '';
        }
        
        function testPageData() {
            const pageData = {
                title: document.title,
                url: window.location.href,
                description: getMetaContent('description'),
                keywords: getMetaContent('keywords')
            };
            
            displayOutput('页面数据测试', pageData);
        }
        
        function testBasicTags() {
            try {
                const tags = getBasicTags();
                displayTagResults('基础标签分析', tags);
                displayOutput('基础标签详细结果', tags);
            } catch (error) {
                displayOutput('基础标签测试错误', { error: error.message, stack: error.stack });
            }
        }
        
        function testTagAnalyzer() {
            try {
                if (typeof window.TagAnalyzer === 'undefined') {
                    throw new Error('TagAnalyzer 未加载');
                }
                
                const analyzer = new window.TagAnalyzer();
                const pageData = {
                    title: document.title,
                    url: window.location.href,
                    description: getMetaContent('description'),
                    keywords: getMetaContent('keywords')
                };
                
                const tags = analyzer.analyzeTags(pageData);
                displayTagResults('TagAnalyzer 分析', tags);
                displayOutput('TagAnalyzer 详细结果', { pageData, tags });
            } catch (error) {
                displayOutput('TagAnalyzer 测试错误', { error: error.message, stack: error.stack });
            }
        }
        
        function getBasicTags() {
            const tags = [];
            const title = document.title.toLowerCase();
            const description = getMetaContent('description').toLowerCase();
            const keywords = getMetaContent('keywords');
            const url = window.location.href.toLowerCase();

            // 基于标题的简单关键词匹配
            const techKeywords = ['javascript', 'python', 'java', 'react', 'vue', '编程', '开发', '代码', 'web'];
            const designKeywords = ['设计', 'ui', 'ux', '界面', '用户体验'];
            const businessKeywords = ['商业', '企业', '产品', '营销', '电商'];

            techKeywords.forEach(keyword => {
                if (title.includes(keyword) || description.includes(keyword)) {
                    tags.push({ tag: '技术', score: 0.7, confidence: 'medium' });
                }
            });

            designKeywords.forEach(keyword => {
                if (title.includes(keyword) || description.includes(keyword)) {
                    tags.push({ tag: '设计', score: 0.7, confidence: 'medium' });
                }
            });

            businessKeywords.forEach(keyword => {
                if (title.includes(keyword) || description.includes(keyword)) {
                    tags.push({ tag: '商业', score: 0.7, confidence: 'medium' });
                }
            });

            // 如果有meta关键词，直接使用
            if (keywords) {
                keywords.split(/[,，、\s]+/).forEach(keyword => {
                    const trimmed = keyword.trim();
                    if (trimmed.length >= 2) {
                        tags.push({ tag: trimmed, score: 0.6, confidence: 'low' });
                    }
                });
            }

            // 去重并排序
            const uniqueTags = tags.reduce((acc, current) => {
                const existing = acc.find(item => item.tag === current.tag);
                if (existing) {
                    existing.score = Math.max(existing.score, current.score);
                } else {
                    acc.push(current);
                }
                return acc;
            }, []);

            return uniqueTags.sort((a, b) => b.score - a.score).slice(0, 8);
        }
        
        function displayTagResults(title, tags) {
            const resultsDiv = document.getElementById('tag-results');
            
            if (!tags || tags.length === 0) {
                resultsDiv.innerHTML = `<h4>${title}</h4><p>未生成任何标签</p>`;
                return;
            }
            
            let html = `<h4>${title}</h4>`;
            tags.forEach(tagInfo => {
                const className = `tag-${tagInfo.confidence}`;
                html += `<span class="tag-result ${className}" title="分数: ${tagInfo.score}, 置信度: ${tagInfo.confidence}">${tagInfo.tag} (${tagInfo.score})</span>`;
            });
            
            resultsDiv.innerHTML = html;
        }
        
        function displayOutput(title, data) {
            const outputDiv = document.getElementById('debug-output');
            const timestamp = new Date().toLocaleTimeString();
            const output = `[${timestamp}] ${title}:\n${JSON.stringify(data, null, 2)}\n\n`;
            outputDiv.textContent += output;
            outputDiv.scrollTop = outputDiv.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('tag-results').innerHTML = '结果已清空';
            document.getElementById('debug-output').textContent = '输出已清空';
        }
    </script>
</body>
</html>
