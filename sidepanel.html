<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebVault - 侧边栏</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles/main.css">
</head>

<body class="sidepanel-body">
    <div class="header">
        <div class="logo">
            <div class="logo-icon">
                <i class="fas fa-box-archive"></i>
            </div>
            <h1>WebVault</h1>
        </div>
        <div class="status">
            <div class="status-dot"></div>
            <span>已连接</span>
        </div>
    </div>

    <div class="main-container">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-info-circle"></i> 基本信息</h2>
                <div class="smart-tags" id="smart-tags">
                    <div class="tags-loading">
                        <i class="fas fa-spinner fa-spin"></i> 智能分析中...
                    </div>
                </div>
            </div>

            <div class="info-grid">
                <div class="info-label"><i class="fas fa-heading"></i> 标题:</div>
                <div class="info-value" id="page-title">正在加载...</div>

                <div class="info-label"><i class="fas fa-link"></i> URL:</div>
                <div class="info-value" id="page-url">正在加载...</div>

                <div class="info-label"><i class="fas fa-quote-left"></i> 描述:</div>
                <div class="info-value" id="page-description">正在加载...</div>

                <div class="info-label"><i class="fas fa-code"></i> 字符集:</div>
                <div class="info-value" id="page-charset">正在加载...</div>

                <div class="info-label"><i class="fas fa-keyword"></i> 关键词:</div>
                <div class="info-value" id="page-keywords">正在加载...</div>

                <div class="info-label"><i class="fas fa-user-tag"></i> 作者:</div>
                <div class="info-value" id="page-author">正在加载...</div>

                <div class="info-label"><i class="fas fa-tags"></i> 智能标签:</div>
                <div class="info-value" id="suggested-tags">正在分析...</div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-camera"></i> 网页截图</h2>
            </div>

            <div class="screenshot-container">
                <div class="screenshot-placeholder" id="screenshot-placeholder">
                    <i class="fas fa-camera" style="font-size: 40px;"></i>
                    <p>点击生成网页截图</p>
                    <small style="color: #888; font-size: 11px; margin-top: 8px; display: block;">
                        💡 提示：系统会自动尝试截取完整页面
                    </small>
                </div>
                <img src="" alt="网页截图" class="screenshot" id="screenshot" style="display: none;">
            </div>

            <div class="progress-bar">
                <div class="progress" id="screenshot-progress"></div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-icons"></i> 网站图标</h2>
            </div>

            <div class="tabs">
                <div class="tab active" data-tab="favicon">主图标</div>
                <div class="tab" data-tab="apple-touch">Apple Touch</div>
                <div class="tab" data-tab="all-icons">所有图标</div>
            </div>

            <div class="tab-content active" id="favicon-tab">
                <div class="logo-container">
                    <div class="logo-preview">
                        <img src="" alt="网站图标" id="favicon-img">
                    </div>
                    <div>
                        <div><strong>主图标URL:</strong> <span id="favicon-url">正在加载...</span></div>
                        <div><strong>尺寸:</strong> <span id="favicon-size">正在加载...</span></div>
                    </div>
                </div>
            </div>

            <div class="tab-content" id="apple-touch-tab">
                <div class="logo-container">
                    <div class="logo-preview">
                        <img src="" alt="Apple Touch 图标" id="apple-touch-img">
                    </div>
                    <div>
                        <div><strong>Apple Touch URL:</strong> <span id="apple-touch-url">正在加载...</span></div>
                        <div><strong>尺寸:</strong> <span id="apple-touch-size">正在加载...</span></div>
                    </div>
                </div>
            </div>

            <div class="tab-content" id="all-icons-tab">
                <div class="icon-grid">
                    <!-- 动态生成图标 -->
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-code"></i> API请求数据</h2>
            </div>

            <div class="metadata-container" id="metadata-container">
                正在生成API请求数据...
            </div>
        </div>

        <div class="action-buttons">
            <button class="btn btn-secondary" id="refresh-btn">
                <i class="fas fa-sync-alt"></i> 重新采集
            </button>
            <button class="btn btn-primary" id="save-btn">
                <i class="fas fa-cloud-upload-alt"></i> 保存到资源库
            </button>
        </div>
    </div>

    <div class="footer">
        WebVault &copy; 2023 - 您的网页信息管理专家
    </div>

    <div class="notification" id="notification">
        数据已成功保存到资源库！
    </div>

    <script type="module" src="scripts/sidepanel.js"></script>
</body>

</html>