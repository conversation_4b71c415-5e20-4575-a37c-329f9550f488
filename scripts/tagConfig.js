/**
 * 标签分析配置文件
 * 管理标签分析的各种设置和规则
 */

window.TagConfig = {
  // 分析器设置
  analyzer: {
    // 使用的分析器类型: 'basic' | 'advanced'
    type: 'basic',
    
    // 最大返回标签数量
    maxTags: 10,
    
    // 最小置信度阈值
    minConfidence: 0.3,
    
    // 算法权重配置
    algorithmWeights: {
      url: 0.25,        // URL模式匹配
      meta: 0.20,       // Meta关键词
      title: 0.20,      // 标题分析
      description: 0.15, // 描述分析
      content: 0.20     // 页面内容分析
    }
  },

  // 标签分类体系
  categories: {
    // 技术开发类
    technology: {
      name: '技术开发',
      keywords: [
        // 编程语言
        'javascript', 'python', 'java', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift', 'kotlin',
        // 前端技术
        'react', 'vue', 'angular', 'html', 'css', 'typescript', 'webpack', 'vite',
        // 后端技术
        'node.js', 'express', 'spring', 'django', 'flask', 'laravel',
        // 数据库
        'mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch',
        // 工具平台
        'github', 'git', 'docker', 'kubernetes', 'aws', 'azure',
        // 中文关键词
        '编程', '开发', '代码', '程序', '软件', '算法', '数据结构', '框架', '库', 'api', '接口'
      ],
      tags: ['技术', '编程', '开发', '代码', '软件开发', '程序设计'],
      weight: 1.2,
      color: '#2196F3'
    },

    // 设计创意类
    design: {
      name: '设计创意',
      keywords: [
        'ui', 'ux', 'design', 'figma', 'sketch', 'photoshop', 'illustrator', 'adobe',
        'prototype', 'wireframe', 'mockup', 'branding', 'logo',
        '设计', '界面', '用户体验', '交互', '视觉', '创意', '美术', '图形', '品牌', '原型'
      ],
      tags: ['设计', 'UI/UX', '用户体验', '视觉设计', '交互设计', '创意'],
      weight: 1.1,
      color: '#E91E63'
    },

    // 商业财经类
    business: {
      name: '商业财经',
      keywords: [
        'business', 'marketing', 'sales', 'finance', 'investment', 'startup', 'entrepreneur',
        'ecommerce', 'retail', 'management', 'strategy', 'consulting',
        '商业', '企业', '公司', '产品', '营销', '销售', '管理', '创业', '投资', '金融', '电商', '零售'
      ],
      tags: ['商业', '企业', '产品', '营销', '销售', '管理', '创业', '投资'],
      weight: 1.0,
      color: '#4CAF50'
    },

    // 教育学习类
    education: {
      name: '教育学习',
      keywords: [
        'education', 'learning', 'course', 'tutorial', 'training', 'university', 'school',
        'knowledge', 'skill', 'certification', 'exam', 'study',
        '教育', '学习', '课程', '教程', '培训', '大学', '学校', '知识', '技能', '考试', '证书', '学习资料'
      ],
      tags: ['教育', '学习', '课程', '培训', '知识', '技能'],
      weight: 1.1,
      color: '#FF9800'
    },

    // 新闻资讯类
    news: {
      name: '新闻资讯',
      keywords: [
        'news', 'media', 'journalism', 'report', 'article', 'press', 'current events',
        'politics', 'society', 'international', 'economy',
        '新闻', '资讯', '媒体', '报道', '文章', '时事', '政治', '社会', '国际', '财经新闻'
      ],
      tags: ['新闻', '资讯', '媒体', '时事', '报道'],
      weight: 1.0,
      color: '#607D8B'
    },

    // 娱乐休闲类
    entertainment: {
      name: '娱乐休闲',
      keywords: [
        'entertainment', 'game', 'gaming', 'movie', 'film', 'music', 'video', 'streaming',
        'sports', 'anime', 'manga', 'celebrity', 'fun', 'leisure',
        '娱乐', '游戏', '电影', '音乐', '视频', '直播', '综艺', '明星', '体育', '动漫', '休闲'
      ],
      tags: ['娱乐', '游戏', '影视', '音乐', '体育', '休闲'],
      weight: 0.9,
      color: '#9C27B0'
    },

    // 生活方式类
    lifestyle: {
      name: '生活方式',
      keywords: [
        'lifestyle', 'health', 'fitness', 'food', 'cooking', 'travel', 'fashion', 'beauty',
        'home', 'family', 'relationship', 'wellness', 'hobby',
        '生活', '健康', '美食', '烹饪', '旅游', '时尚', '美容', '家居', '家庭', '爱好', '养生'
      ],
      tags: ['生活', '健康', '美食', '旅游', '时尚', '家居'],
      weight: 0.8,
      color: '#795548'
    },

    // 科学技术类
    science: {
      name: '科学技术',
      keywords: [
        'science', 'research', 'technology', 'innovation', 'ai', 'artificial intelligence',
        'machine learning', 'data science', 'blockchain', 'iot', 'robotics',
        '科学', '研究', '技术', '创新', '人工智能', '机器学习', '数据科学', '区块链', '物联网', '机器人'
      ],
      tags: ['科学', '技术', '研究', '创新', '人工智能', '数据科学'],
      weight: 1.1,
      color: '#00BCD4'
    }
  },

  // URL模式匹配规则
  urlPatterns: {
    // 技术平台
    'github.com': { tags: ['技术', '开源', '代码'], confidence: 0.95, category: 'technology' },
    'gitlab.com': { tags: ['技术', '开源', '代码'], confidence: 0.95, category: 'technology' },
    'stackoverflow.com': { tags: ['技术', '编程', '问答'], confidence: 0.95, category: 'technology' },
    'codepen.io': { tags: ['技术', '前端', '代码'], confidence: 0.9, category: 'technology' },
    'jsfiddle.net': { tags: ['技术', '前端', '代码'], confidence: 0.9, category: 'technology' },
    
    // 设计平台
    'dribbble.com': { tags: ['设计', '创意', '视觉'], confidence: 0.9, category: 'design' },
    'behance.net': { tags: ['设计', '创意', '作品集'], confidence: 0.9, category: 'design' },
    'figma.com': { tags: ['设计', 'UI/UX', '原型'], confidence: 0.9, category: 'design' },
    
    // 学习平台
    'coursera.org': { tags: ['教育', '课程', '学习'], confidence: 0.95, category: 'education' },
    'udemy.com': { tags: ['教育', '课程', '培训'], confidence: 0.95, category: 'education' },
    'edx.org': { tags: ['教育', '大学', '课程'], confidence: 0.95, category: 'education' },
    'khan academy.org': { tags: ['教育', '学习', '免费课程'], confidence: 0.95, category: 'education' },
    
    // 中文平台
    'zhihu.com': { tags: ['知识', '问答', '社区'], confidence: 0.8, category: 'education' },
    'csdn.net': { tags: ['技术', '编程', '博客'], confidence: 0.9, category: 'technology' },
    'juejin.cn': { tags: ['技术', '前端', '开发'], confidence: 0.9, category: 'technology' },
    'segmentfault.com': { tags: ['技术', '编程', '问答'], confidence: 0.9, category: 'technology' },
    'oschina.net': { tags: ['技术', '开源', '社区'], confidence: 0.8, category: 'technology' },
    'cnblogs.com': { tags: ['技术', '博客', '编程'], confidence: 0.8, category: 'technology' },
    
    // 视频平台
    'bilibili.com': { tags: ['视频', '娱乐', '学习'], confidence: 0.9, category: 'entertainment' },
    'youtube.com': { tags: ['视频', '娱乐', '教育'], confidence: 0.8, category: 'entertainment' },
    'youku.com': { tags: ['视频', '娱乐', '影视'], confidence: 0.8, category: 'entertainment' },
    
    // 电商平台
    'taobao.com': { tags: ['电商', '购物', '商业'], confidence: 0.95, category: 'business' },
    'tmall.com': { tags: ['电商', '购物', '品牌'], confidence: 0.95, category: 'business' },
    'jd.com': { tags: ['电商', '购物', '商业'], confidence: 0.95, category: 'business' },
    'amazon.com': { tags: ['电商', '购物', '商业'], confidence: 0.95, category: 'business' },
    
    // 新闻媒体
    'xinhuanet.com': { tags: ['新闻', '资讯', '媒体'], confidence: 0.95, category: 'news' },
    'people.com.cn': { tags: ['新闻', '资讯', '媒体'], confidence: 0.95, category: 'news' },
    'cnn.com': { tags: ['新闻', '资讯', '国际'], confidence: 0.95, category: 'news' },
    'bbc.com': { tags: ['新闻', '资讯', '国际'], confidence: 0.95, category: 'news' }
  },

  // 停用词列表
  stopWords: {
    chinese: [
      '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '里', '后', '以', '时', '来', '用', '们', '生', '大', '为', '能', '作', '分', '成', '者', '多', '部', '可', '小', '些', '主', '样', '理', '心', '她', '本', '前', '开', '但', '因', '只', '从', '想', '实'
    ],
    english: [
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their'
    ]
  },

  // 标签优化规则
  optimization: {
    // 标签合并规则
    mergeRules: {
      '编程': ['代码', '程序设计'],
      '设计': ['UI设计', 'UX设计'],
      '商业': ['企业', '公司'],
      '学习': ['教育', '培训']
    },
    
    // 标签过滤规则
    filterRules: {
      minLength: 2,        // 最小标签长度
      maxLength: 20,       // 最大标签长度
      excludeNumbers: true, // 排除纯数字标签
      excludeSpecialChars: true // 排除特殊字符标签
    }
  }
};
