/**
 * 网页标签自动识别器
 * 基于多数据源的智能标签提取算法
 */

class TagAnalyzer {
  constructor() {
    // 预定义的标签分类体系
    this.tagCategories = {
      // 技术类
      technology: {
        keywords: ['编程', '开发', '代码', 'javascript', 'python', 'java', 'react', 'vue', 'angular', 'node', 'api', '框架', '库', '工具', 'github', 'git', '算法', '数据结构', '机器学习', 'ai', '人工智能'],
        weight: 1.2,
        tags: ['技术', '编程', '开发', '工具']
      },
      
      // 设计类
      design: {
        keywords: ['设计', 'ui', 'ux', '界面', '用户体验', 'figma', 'sketch', 'photoshop', '原型', '交互', '视觉', '品牌', 'logo'],
        weight: 1.1,
        tags: ['设计', 'UI/UX', '视觉设计', '交互设计']
      },
      
      // 商业类
      business: {
        keywords: ['商业', '企业', '公司', '产品', '营销', '销售', '管理', '创业', '投资', '金融', '电商', '购物', '支付'],
        weight: 1.0,
        tags: ['商业', '企业', '产品', '营销', '电商']
      },
      
      // 教育类
      education: {
        keywords: ['教育', '学习', '课程', '教程', '培训', '大学', '学校', '知识', '技能', '考试', '证书'],
        weight: 1.1,
        tags: ['教育', '学习', '课程', '培训']
      },
      
      // 新闻媒体
      news: {
        keywords: ['新闻', '资讯', '媒体', '报道', '时事', '政治', '社会', '国际', '财经', '科技新闻'],
        weight: 1.0,
        tags: ['新闻', '资讯', '媒体', '时事']
      },
      
      // 娱乐类
      entertainment: {
        keywords: ['娱乐', '游戏', '电影', '音乐', '视频', '直播', '综艺', '明星', '体育', '动漫'],
        weight: 0.9,
        tags: ['娱乐', '游戏', '影视', '音乐', '体育']
      },
      
      // 生活类
      lifestyle: {
        keywords: ['生活', '健康', '美食', '旅游', '时尚', '美容', '家居', '汽车', '母婴', '宠物'],
        weight: 0.8,
        tags: ['生活', '健康', '美食', '旅游', '时尚']
      }
    };

    // URL模式匹配规则
    this.urlPatterns = {
      'github.com': ['技术', '开发', '代码'],
      'stackoverflow.com': ['技术', '编程', '问答'],
      'medium.com': ['博客', '技术', '分享'],
      'zhihu.com': ['知识', '问答', '分享'],
      'csdn.net': ['技术', '编程', '博客'],
      'juejin.cn': ['技术', '前端', '开发'],
      'bilibili.com': ['视频', '娱乐', '学习'],
      'youtube.com': ['视频', '娱乐', '教育'],
      'taobao.com': ['电商', '购物', '商业'],
      'jd.com': ['电商', '购物', '商业'],
      'baidu.com': ['搜索', '工具'],
      'google.com': ['搜索', '工具'],
      'wikipedia.org': ['知识', '百科', '教育']
    };
  }

  /**
   * 主要的标签分析函数
   * @param {Object} pageData - 页面数据
   * @returns {Array} 建议的标签列表
   */
  analyzeTags(pageData) {
    const tagScores = new Map();
    
    // 1. 分析URL模式
    this.analyzeUrlPattern(pageData.url, tagScores);
    
    // 2. 分析meta关键词
    this.analyzeMetaKeywords(pageData.keywords, tagScores);
    
    // 3. 分析标题
    this.analyzeTitle(pageData.title, tagScores);
    
    // 4. 分析描述
    this.analyzeDescription(pageData.description, tagScores);
    
    // 5. 分析页面内容
    this.analyzePageContent(tagScores);
    
    // 6. 计算最终标签
    return this.calculateFinalTags(tagScores);
  }

  /**
   * 分析URL模式
   */
  analyzeUrlPattern(url, tagScores) {
    try {
      const domain = new URL(url).hostname.toLowerCase();
      
      for (const [pattern, tags] of Object.entries(this.urlPatterns)) {
        if (domain.includes(pattern)) {
          tags.forEach(tag => {
            this.addScore(tagScores, tag, 2.0); // URL匹配权重较高
          });
        }
      }
    } catch (error) {
      console.warn('URL分析失败:', error);
    }
  }

  /**
   * 分析meta关键词
   */
  analyzeMetaKeywords(keywords, tagScores) {
    if (!keywords) return;
    
    const keywordList = keywords.split(/[,，、\s]+/).filter(k => k.trim());
    keywordList.forEach(keyword => {
      const normalizedKeyword = keyword.trim().toLowerCase();
      
      // 直接使用meta关键词作为标签候选
      this.addScore(tagScores, keyword.trim(), 1.5);
      
      // 匹配预定义分类
      this.matchKeywordToCategories(normalizedKeyword, tagScores);
    });
  }

  /**
   * 分析标题
   */
  analyzeTitle(title, tagScores) {
    if (!title) return;
    
    const titleWords = this.extractKeywords(title);
    titleWords.forEach(word => {
      this.addScore(tagScores, word, 1.3);
      this.matchKeywordToCategories(word.toLowerCase(), tagScores);
    });
  }

  /**
   * 分析描述
   */
  analyzeDescription(description, tagScores) {
    if (!description) return;
    
    const descWords = this.extractKeywords(description);
    descWords.forEach(word => {
      this.addScore(tagScores, word, 1.0);
      this.matchKeywordToCategories(word.toLowerCase(), tagScores);
    });
  }

  /**
   * 分析页面内容
   */
  analyzePageContent(tagScores) {
    // 获取页面主要标题
    const headings = document.querySelectorAll('h1, h2, h3');
    headings.forEach(heading => {
      const words = this.extractKeywords(heading.textContent);
      words.forEach(word => {
        this.addScore(tagScores, word, 0.8);
        this.matchKeywordToCategories(word.toLowerCase(), tagScores);
      });
    });

    // 分析页面主要文本内容（限制长度避免性能问题）
    const textContent = document.body.textContent.slice(0, 2000);
    const contentWords = this.extractKeywords(textContent);
    const wordFreq = this.calculateWordFrequency(contentWords);
    
    // 只取频率最高的词
    Object.entries(wordFreq)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20)
      .forEach(([word, freq]) => {
        this.addScore(tagScores, word, Math.min(freq * 0.1, 0.5));
        this.matchKeywordToCategories(word.toLowerCase(), tagScores);
      });
  }

  /**
   * 匹配关键词到预定义分类
   */
  matchKeywordToCategories(keyword, tagScores) {
    for (const [categoryName, category] of Object.entries(this.tagCategories)) {
      if (category.keywords.some(k => keyword.includes(k) || k.includes(keyword))) {
        category.tags.forEach(tag => {
          this.addScore(tagScores, tag, category.weight);
        });
      }
    }
  }

  /**
   * 提取关键词
   */
  extractKeywords(text) {
    if (!text) return [];
    
    // 简单的中英文关键词提取
    const words = text
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, ' ') // 保留中文、英文、数字
      .split(/\s+/)
      .filter(word => {
        const trimmed = word.trim();
        return trimmed.length >= 2 && trimmed.length <= 20; // 过滤长度
      })
      .map(word => word.trim());
    
    return [...new Set(words)]; // 去重
  }

  /**
   * 计算词频
   */
  calculateWordFrequency(words) {
    const freq = {};
    words.forEach(word => {
      freq[word] = (freq[word] || 0) + 1;
    });
    return freq;
  }

  /**
   * 添加分数
   */
  addScore(tagScores, tag, score) {
    if (!tag || tag.length < 2) return;
    tagScores.set(tag, (tagScores.get(tag) || 0) + score);
  }

  /**
   * 计算最终标签
   */
  calculateFinalTags(tagScores) {
    // 按分数排序
    const sortedTags = Array.from(tagScores.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10) // 最多返回10个标签
      .filter(([tag, score]) => score >= 0.5) // 过滤低分标签
      .map(([tag, score]) => ({
        tag: tag,
        score: Math.round(score * 100) / 100,
        confidence: this.calculateConfidence(score)
      }));

    return sortedTags;
  }

  /**
   * 计算置信度
   */
  calculateConfidence(score) {
    if (score >= 3) return 'high';
    if (score >= 1.5) return 'medium';
    return 'low';
  }
}

// 导出标签分析器
window.TagAnalyzer = TagAnalyzer;
