/**
 * WebVault 配置页面脚本
 */

// 默认配置
const DEFAULT_CONFIG = {
  apiUrl: '',
  apiKey: '',
  timeout: 30,
  maxTags: 10,
  minConfidence: 0.3
};

// DOM元素
let elements = {};

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  console.log('Options page loaded');
  initializeElements();
  loadConfig();
  attachEventListeners();
});

/**
 * 初始化DOM元素引用
 */
function initializeElements() {
  elements = {
    apiUrl: document.getElementById('api-url'),
    apiKey: document.getElementById('api-key'),
    timeout: document.getElementById('timeout'),
    maxTags: document.getElementById('max-tags'),
    minConfidence: document.getElementById('min-confidence'),
    testConnection: document.getElementById('test-connection'),
    testResult: document.getElementById('test-result'),
    connectionStatus: document.getElementById('connection-status'),
    saveConfig: document.getElementById('save-config'),
    resetConfig: document.getElementById('reset-config'),
    notification: document.getElementById('notification')
  };
}

/**
 * 绑定事件监听器
 */
function attachEventListeners() {
  elements.saveConfig.addEventListener('click', saveConfig);
  elements.resetConfig.addEventListener('click', resetConfig);
  elements.testConnection.addEventListener('click', testConnection);
  
  // 输入验证
  elements.apiUrl.addEventListener('blur', validateApiUrl);
  elements.timeout.addEventListener('change', validateTimeout);
  elements.maxTags.addEventListener('change', validateMaxTags);
  elements.minConfidence.addEventListener('change', validateMinConfidence);
}

/**
 * 加载配置
 */
async function loadConfig() {
  try {
    const result = await chrome.storage.sync.get(DEFAULT_CONFIG);
    
    elements.apiUrl.value = result.apiUrl || '';
    elements.apiKey.value = result.apiKey || '';
    elements.timeout.value = result.timeout || 30;
    elements.maxTags.value = result.maxTags || 10;
    elements.minConfidence.value = result.minConfidence || 0.3;
    
    console.log('Config loaded:', result);
    
    // 如果有API地址，显示连接状态
    if (result.apiUrl) {
      updateConnectionStatus('未测试', 'warning');
    }
  } catch (error) {
    console.error('Error loading config:', error);
    showNotification('加载配置失败', 'error');
  }
}

/**
 * 保存配置
 */
async function saveConfig() {
  try {
    // 验证输入
    if (!validateAllInputs()) {
      return;
    }
    
    const config = {
      apiUrl: elements.apiUrl.value.trim(),
      apiKey: elements.apiKey.value.trim(),
      timeout: parseInt(elements.timeout.value),
      maxTags: parseInt(elements.maxTags.value),
      minConfidence: parseFloat(elements.minConfidence.value)
    };
    
    // 显示保存中状态
    elements.saveConfig.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
    elements.saveConfig.disabled = true;
    
    await chrome.storage.sync.set(config);
    
    console.log('Config saved:', config);
    showNotification('配置保存成功！', 'success');
    
    // 如果有API地址，提示测试连接
    if (config.apiUrl) {
      updateConnectionStatus('配置已保存，建议测试连接', 'warning');
    }
  } catch (error) {
    console.error('Error saving config:', error);
    showNotification('保存配置失败: ' + error.message, 'error');
  } finally {
    elements.saveConfig.innerHTML = '<i class="fas fa-save"></i> 保存配置';
    elements.saveConfig.disabled = false;
  }
}

/**
 * 重置配置
 */
async function resetConfig() {
  if (!confirm('确定要重置所有配置到默认值吗？')) {
    return;
  }
  
  try {
    await chrome.storage.sync.clear();
    
    elements.apiUrl.value = '';
    elements.apiKey.value = '';
    elements.timeout.value = 30;
    elements.maxTags.value = 10;
    elements.minConfidence.value = 0.3;
    
    elements.testResult.style.display = 'none';
    elements.connectionStatus.innerHTML = '';
    
    showNotification('配置已重置为默认值', 'success');
    console.log('Config reset to defaults');
  } catch (error) {
    console.error('Error resetting config:', error);
    showNotification('重置配置失败: ' + error.message, 'error');
  }
}

/**
 * 测试API连接
 */
async function testConnection() {
  const apiUrl = elements.apiUrl.value.trim();
  
  if (!apiUrl) {
    showNotification('请先输入API接口地址', 'warning');
    return;
  }
  
  if (!validateApiUrl()) {
    return;
  }
  
  try {
    // 显示测试中状态
    elements.testConnection.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';
    elements.testConnection.disabled = true;
    elements.testResult.style.display = 'none';
    updateConnectionStatus('测试中...', 'warning');
    
    const testData = {
      test: true,
      timestamp: Date.now(),
      source: 'WebVault Configuration Test'
    };
    
    const timeout = parseInt(elements.timeout.value) * 1000;
    const apiKey = elements.apiKey.value.trim();
    
    const headers = {
      'Content-Type': 'application/json'
    };
    
    if (apiKey) {
      headers['Authorization'] = `Bearer ${apiKey}`;
    }
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(testData),
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    const responseText = await response.text();
    let responseData;
    
    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = { raw: responseText };
    }
    
    const testResult = {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      data: responseData,
      timestamp: new Date().toISOString()
    };
    
    elements.testResult.textContent = JSON.stringify(testResult, null, 2);
    elements.testResult.style.display = 'block';
    
    if (response.ok) {
      updateConnectionStatus('连接成功', 'success');
      showNotification('API连接测试成功！', 'success');
    } else {
      updateConnectionStatus(`连接失败 (${response.status})`, 'error');
      showNotification(`API连接失败: ${response.status} ${response.statusText}`, 'error');
    }
    
  } catch (error) {
    console.error('Connection test failed:', error);
    
    let errorMessage = '连接失败';
    if (error.name === 'AbortError') {
      errorMessage = '请求超时';
    } else if (error.message.includes('Failed to fetch')) {
      errorMessage = '网络错误或CORS问题';
    } else {
      errorMessage = error.message;
    }
    
    elements.testResult.textContent = `错误: ${errorMessage}\n时间: ${new Date().toISOString()}`;
    elements.testResult.style.display = 'block';
    
    updateConnectionStatus(errorMessage, 'error');
    showNotification(`连接测试失败: ${errorMessage}`, 'error');
  } finally {
    elements.testConnection.innerHTML = '<i class="fas fa-plug"></i> 测试连接';
    elements.testConnection.disabled = false;
  }
}

/**
 * 验证所有输入
 */
function validateAllInputs() {
  return validateApiUrl() && 
         validateTimeout() && 
         validateMaxTags() && 
         validateMinConfidence();
}

/**
 * 验证API地址
 */
function validateApiUrl() {
  const url = elements.apiUrl.value.trim();
  
  if (url && !isValidUrl(url)) {
    showNotification('请输入有效的API地址', 'error');
    elements.apiUrl.focus();
    return false;
  }
  
  return true;
}

/**
 * 验证超时时间
 */
function validateTimeout() {
  const timeout = parseInt(elements.timeout.value);
  
  if (timeout < 5 || timeout > 120) {
    showNotification('超时时间必须在5-120秒之间', 'error');
    elements.timeout.focus();
    return false;
  }
  
  return true;
}

/**
 * 验证最大标签数
 */
function validateMaxTags() {
  const maxTags = parseInt(elements.maxTags.value);
  
  if (maxTags < 3 || maxTags > 20) {
    showNotification('最大标签数必须在3-20之间', 'error');
    elements.maxTags.focus();
    return false;
  }
  
  return true;
}

/**
 * 验证最小置信度
 */
function validateMinConfidence() {
  const minConfidence = parseFloat(elements.minConfidence.value);
  
  if (minConfidence < 0.1 || minConfidence > 1.0) {
    showNotification('最小置信度必须在0.1-1.0之间', 'error');
    elements.minConfidence.focus();
    return false;
  }
  
  return true;
}

/**
 * 检查URL是否有效
 */
function isValidUrl(string) {
  try {
    const url = new URL(string);
    return url.protocol === 'http:' || url.protocol === 'https:';
  } catch {
    return false;
  }
}

/**
 * 更新连接状态显示
 */
function updateConnectionStatus(message, type) {
  const statusClass = `status-${type}`;
  const icon = type === 'success' ? 'check-circle' : 
               type === 'error' ? 'times-circle' : 'exclamation-triangle';
  
  elements.connectionStatus.innerHTML = `
    <div class="status-indicator ${statusClass}">
      <i class="fas fa-${icon}"></i>
      ${message}
    </div>
  `;
}

/**
 * 显示通知
 */
function showNotification(message, type = 'info') {
  elements.notification.textContent = message;
  elements.notification.className = `notification ${type} show`;
  
  setTimeout(() => {
    elements.notification.classList.remove('show');
  }, 3000);
}
