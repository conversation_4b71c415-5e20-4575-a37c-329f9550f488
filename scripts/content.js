/**
 * WebVault Content Script
 * 运行在网页中，负责获取页面信息和元数据
 */

// 避免重复注入
if (typeof window.webVaultContentScript === "undefined") {
  window.webVaultContentScript = true;

  console.log("WebVault content script loaded");

  // 监听来自background脚本的消息
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log("Content script received message:", request);

    switch (request.action) {
      case "getPageInfo":
        getPageInfo().then(sendResponse);
        return true; // 保持消息通道开启

      default:
        sendResponse({ error: "Unknown action in content script" });
    }
  });

  /**
   * 获取页面的所有信息
   */
  async function getPageInfo() {
    try {
      const pageInfo = {
        // 基本信息
        title: getPageTitle(),
        url: window.location.href,
        description: getMetaContent("description"),
        keywords: getMetaContent("keywords"),
        author: getMetaContent("author"),
        charset: getCharset(),

        // Open Graph 数据
        openGraph: getOpenGraphData(),

        // Twitter Card 数据
        twitterCard: getTwitterCardData(),

        // 网站图标
        favicon: getFavicon(),
        appleTouchIcon: getAppleTouchIcon(),
        allIcons: getAllIcons(),

        // 技术信息
        doctype: getDoctypeInfo(),
        viewport: getMetaContent("viewport"),
        generator: getMetaContent("generator"),

        // 结构化数据
        structuredData: getStructuredData(),

        // 页面统计
        stats: getPageStats(),

        // 元数据
        allMetaTags: getAllMetaTags(),

        // 智能标签分析
        suggestedTags: getSuggestedTags(),

        // 时间戳
        timestamp: Date.now(),
        collectedAt: new Date().toISOString(),
      };

      return { success: true, data: pageInfo };
    } catch (error) {
      console.error("Error getting page info:", error);
      return { error: error.message };
    }
  }

  /**
   * 获取页面标题
   */
  function getPageTitle() {
    return document.title || "";
  }

  /**
   * 获取meta标签内容
   */
  function getMetaContent(name) {
    const meta = document.querySelector(
      `meta[name="${name}"], meta[property="${name}"]`
    );
    return meta ? meta.getAttribute("content") || "" : "";
  }

  /**
   * 获取字符集
   */
  function getCharset() {
    const charset = document.querySelector("meta[charset]");
    if (charset) return charset.getAttribute("charset");

    const httpEquiv = document.querySelector('meta[http-equiv="Content-Type"]');
    if (httpEquiv) {
      const content = httpEquiv.getAttribute("content") || "";
      const match = content.match(/charset=([^;]+)/i);
      return match ? match[1] : "";
    }

    return "UTF-8"; // 默认值
  }

  /**
   * 获取Open Graph数据
   */
  function getOpenGraphData() {
    const ogTags = document.querySelectorAll('meta[property^="og:"]');
    const ogData = {};

    ogTags.forEach((tag) => {
      const property = tag.getAttribute("property");
      const content = tag.getAttribute("content");
      if (property && content) {
        const key = property.replace("og:", "");
        ogData[key] = content;
      }
    });

    return ogData;
  }

  /**
   * 获取Twitter Card数据
   */
  function getTwitterCardData() {
    const twitterTags = document.querySelectorAll('meta[name^="twitter:"]');
    const twitterData = {};

    twitterTags.forEach((tag) => {
      const name = tag.getAttribute("name");
      const content = tag.getAttribute("content");
      if (name && content) {
        const key = name.replace("twitter:", "");
        twitterData[key] = content;
      }
    });

    return twitterData;
  }

  /**
   * 获取网站图标
   */
  function getFavicon() {
    // 尝试多种方式获取favicon
    const iconSelectors = [
      'link[rel="icon"]',
      'link[rel="shortcut icon"]',
      'link[rel="apple-touch-icon"]',
      'link[rel="icon"][type="image/x-icon"]',
      'link[rel="icon"][type="image/png"]',
    ];

    for (const selector of iconSelectors) {
      const link = document.querySelector(selector);
      if (link) {
        const href = link.getAttribute("href");
        if (href) {
          return {
            url: new URL(href, window.location.href).href,
            sizes: link.getAttribute("sizes") || "",
            type: link.getAttribute("type") || "",
          };
        }
      }
    }

    // 默认favicon路径
    return {
      url: new URL("/favicon.ico", window.location.href).href,
      sizes: "",
      type: "image/x-icon",
    };
  }

  /**
   * 获取Apple Touch Icon
   */
  function getAppleTouchIcon() {
    const appleTouchIcon = document.querySelector(
      'link[rel="apple-touch-icon"], link[rel="apple-touch-icon-precomposed"]'
    );
    if (appleTouchIcon) {
      const href = appleTouchIcon.getAttribute("href");
      if (href) {
        return {
          url: new URL(href, window.location.href).href,
          sizes: appleTouchIcon.getAttribute("sizes") || "",
          type: appleTouchIcon.getAttribute("type") || "",
        };
      }
    }
    return null;
  }

  /**
   * 获取所有图标
   */
  function getAllIcons() {
    const icons = [];
    const iconLinks = document.querySelectorAll('link[rel*="icon"]');

    iconLinks.forEach((link) => {
      const href = link.getAttribute("href");
      if (href) {
        icons.push({
          rel: link.getAttribute("rel"),
          url: new URL(href, window.location.href).href,
          sizes: link.getAttribute("sizes") || "",
          type: link.getAttribute("type") || "",
        });
      }
    });

    return icons;
  }

  /**
   * 获取DOCTYPE信息
   */
  function getDoctypeInfo() {
    const doctype = document.doctype;
    if (doctype) {
      return {
        name: doctype.name,
        publicId: doctype.publicId,
        systemId: doctype.systemId,
      };
    }
    return null;
  }

  /**
   * 获取结构化数据
   */
  function getStructuredData() {
    const scripts = document.querySelectorAll(
      'script[type="application/ld+json"]'
    );
    const structuredData = [];

    scripts.forEach((script) => {
      try {
        const data = JSON.parse(script.textContent);
        structuredData.push(data);
      } catch (error) {
        console.log("Error parsing structured data:", error);
      }
    });

    return structuredData;
  }

  /**
   * 获取页面统计信息
   */
  function getPageStats() {
    return {
      totalElements: document.querySelectorAll("*").length,
      totalImages: document.querySelectorAll("img").length,
      totalLinks: document.querySelectorAll("a").length,
      totalScripts: document.querySelectorAll("script").length,
      totalStylesheets: document.querySelectorAll('link[rel="stylesheet"]')
        .length,
      headings: {
        h1: document.querySelectorAll("h1").length,
        h2: document.querySelectorAll("h2").length,
        h3: document.querySelectorAll("h3").length,
        h4: document.querySelectorAll("h4").length,
        h5: document.querySelectorAll("h5").length,
        h6: document.querySelectorAll("h6").length,
      },
    };
  }

  /**
   * 获取所有meta标签
   */
  function getAllMetaTags() {
    const metaTags = document.querySelectorAll("meta");
    const allMeta = [];

    metaTags.forEach((meta) => {
      const metaInfo = {};

      // 获取所有属性
      Array.from(meta.attributes).forEach((attr) => {
        metaInfo[attr.name] = attr.value;
      });

      allMeta.push(metaInfo);
    });

    return allMeta;
  }

  /**
   * 获取智能推荐标签
   */
  function getSuggestedTags() {
    try {
      const pageData = {
        title: getPageTitle(),
        url: window.location.href,
        description: getMetaContent("description"),
        keywords: getMetaContent("keywords")
      };

      console.log("开始标签分析，页面数据:", pageData);

      // 使用基础分析器（同步）
      if (typeof window.TagAnalyzer !== 'undefined') {
        console.log("使用基础标签分析器");
        const analyzer = new window.TagAnalyzer();
        const result = analyzer.analyzeTags(pageData);
        console.log("基础分析器结果:", result);
        return result;
      }

      // 降级到基础标签
      console.log("使用基础标签方案");
      const result = getBasicTags();
      console.log("基础标签结果:", result);
      return result;
    } catch (error) {
      console.warn("标签分析失败，使用基础标签:", error);
      return getBasicTags();
    }
  }

  /**
   * 获取基础标签（备用方案）
   */
  function getBasicTags() {
    const tags = [];
    const title = getPageTitle().toLowerCase();
    const description = getMetaContent("description").toLowerCase();
    const keywords = getMetaContent("keywords");
    const url = window.location.href.toLowerCase();

    // 基于URL的简单分类
    if (url.includes('github.com')) tags.push({ tag: '技术', score: 0.9, confidence: 'high' });
    if (url.includes('stackoverflow.com')) tags.push({ tag: '编程', score: 0.9, confidence: 'high' });
    if (url.includes('zhihu.com')) tags.push({ tag: '知识分享', score: 0.8, confidence: 'medium' });
    if (url.includes('bilibili.com')) tags.push({ tag: '视频', score: 0.8, confidence: 'medium' });
    if (url.includes('taobao.com') || url.includes('jd.com')) tags.push({ tag: '电商', score: 0.9, confidence: 'high' });

    // 基于标题的简单关键词匹配
    const techKeywords = ['javascript', 'python', 'java', 'react', 'vue', '编程', '开发', '代码'];
    const designKeywords = ['设计', 'ui', 'ux', '界面', '用户体验'];
    const businessKeywords = ['商业', '企业', '产品', '营销', '电商'];

    techKeywords.forEach(keyword => {
      if (title.includes(keyword) || description.includes(keyword)) {
        tags.push({ tag: '技术', score: 0.7, confidence: 'medium' });
      }
    });

    designKeywords.forEach(keyword => {
      if (title.includes(keyword) || description.includes(keyword)) {
        tags.push({ tag: '设计', score: 0.7, confidence: 'medium' });
      }
    });

    businessKeywords.forEach(keyword => {
      if (title.includes(keyword) || description.includes(keyword)) {
        tags.push({ tag: '商业', score: 0.7, confidence: 'medium' });
      }
    });

    // 如果有meta关键词，直接使用
    if (keywords) {
      keywords.split(/[,，、\s]+/).forEach(keyword => {
        const trimmed = keyword.trim();
        if (trimmed.length >= 2) {
          tags.push({ tag: trimmed, score: 0.6, confidence: 'low' });
        }
      });
    }

    // 去重并排序
    const uniqueTags = tags.reduce((acc, current) => {
      const existing = acc.find(item => item.tag === current.tag);
      if (existing) {
        existing.score = Math.max(existing.score, current.score);
      } else {
        acc.push(current);
      }
      return acc;
    }, []);

    return uniqueTags.sort((a, b) => b.score - a.score).slice(0, 8);
  }
}

// 页面加载完成后自动发送页面信息（可选）
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", () => {
    console.log("WebVault: DOM loaded");
  });
} else {
  console.log("WebVault: DOM already loaded");
}
