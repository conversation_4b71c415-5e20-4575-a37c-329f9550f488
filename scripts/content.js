/**
 * WebVault Content Script
 * 运行在网页中，负责获取页面信息和元数据
 */

// 避免重复注入
if (typeof window.webVaultContentScript === "undefined") {
  window.webVaultContentScript = true;

  console.log("WebVault content script loaded");

  // 监听来自background脚本的消息
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log("Content script received message:", request);

    switch (request.action) {
      case "getPageInfo":
        getPageInfo().then(sendResponse);
        return true; // 保持消息通道开启

      default:
        sendResponse({ error: "Unknown action in content script" });
    }
  });

  /**
   * 获取页面的所有信息
   */
  async function getPageInfo() {
    try {
      const pageInfo = {
        // 基本信息
        title: getPageTitle(),
        url: window.location.href,
        description: getMetaContent("description"),
        keywords: getMetaContent("keywords"),
        author: getMetaContent("author"),
        charset: getCharset(),

        // Open Graph 数据
        openGraph: getOpenGraphData(),

        // Twitter Card 数据
        twitterCard: getTwitterCardData(),

        // 网站图标
        favicon: getFavicon(),
        appleTouchIcon: getAppleTouchIcon(),
        allIcons: getAllIcons(),

        // 技术信息
        doctype: getDoctypeInfo(),
        viewport: getMetaContent("viewport"),
        generator: getMetaContent("generator"),

        // 结构化数据
        structuredData: getStructuredData(),

        // 页面统计
        stats: getPageStats(),

        // 元数据
        allMetaTags: getAllMetaTags(),

        // 时间戳
        timestamp: Date.now(),
        collectedAt: new Date().toISOString(),
      };

      return { success: true, data: pageInfo };
    } catch (error) {
      console.error("Error getting page info:", error);
      return { error: error.message };
    }
  }

  /**
   * 获取页面标题
   */
  function getPageTitle() {
    return document.title || "";
  }

  /**
   * 获取meta标签内容
   */
  function getMetaContent(name) {
    const meta = document.querySelector(
      `meta[name="${name}"], meta[property="${name}"]`
    );
    return meta ? meta.getAttribute("content") || "" : "";
  }

  /**
   * 获取字符集
   */
  function getCharset() {
    const charset = document.querySelector("meta[charset]");
    if (charset) return charset.getAttribute("charset");

    const httpEquiv = document.querySelector('meta[http-equiv="Content-Type"]');
    if (httpEquiv) {
      const content = httpEquiv.getAttribute("content") || "";
      const match = content.match(/charset=([^;]+)/i);
      return match ? match[1] : "";
    }

    return "UTF-8"; // 默认值
  }

  /**
   * 获取Open Graph数据
   */
  function getOpenGraphData() {
    const ogTags = document.querySelectorAll('meta[property^="og:"]');
    const ogData = {};

    ogTags.forEach((tag) => {
      const property = tag.getAttribute("property");
      const content = tag.getAttribute("content");
      if (property && content) {
        const key = property.replace("og:", "");
        ogData[key] = content;
      }
    });

    return ogData;
  }

  /**
   * 获取Twitter Card数据
   */
  function getTwitterCardData() {
    const twitterTags = document.querySelectorAll('meta[name^="twitter:"]');
    const twitterData = {};

    twitterTags.forEach((tag) => {
      const name = tag.getAttribute("name");
      const content = tag.getAttribute("content");
      if (name && content) {
        const key = name.replace("twitter:", "");
        twitterData[key] = content;
      }
    });

    return twitterData;
  }

  /**
   * 获取网站图标
   */
  function getFavicon() {
    // 尝试多种方式获取favicon
    const iconSelectors = [
      'link[rel="icon"]',
      'link[rel="shortcut icon"]',
      'link[rel="apple-touch-icon"]',
      'link[rel="icon"][type="image/x-icon"]',
      'link[rel="icon"][type="image/png"]',
    ];

    for (const selector of iconSelectors) {
      const link = document.querySelector(selector);
      if (link) {
        const href = link.getAttribute("href");
        if (href) {
          return {
            url: new URL(href, window.location.href).href,
            sizes: link.getAttribute("sizes") || "",
            type: link.getAttribute("type") || "",
          };
        }
      }
    }

    // 默认favicon路径
    return {
      url: new URL("/favicon.ico", window.location.href).href,
      sizes: "",
      type: "image/x-icon",
    };
  }

  /**
   * 获取Apple Touch Icon
   */
  function getAppleTouchIcon() {
    const appleTouchIcon = document.querySelector(
      'link[rel="apple-touch-icon"], link[rel="apple-touch-icon-precomposed"]'
    );
    if (appleTouchIcon) {
      const href = appleTouchIcon.getAttribute("href");
      if (href) {
        return {
          url: new URL(href, window.location.href).href,
          sizes: appleTouchIcon.getAttribute("sizes") || "",
          type: appleTouchIcon.getAttribute("type") || "",
        };
      }
    }
    return null;
  }

  /**
   * 获取所有图标
   */
  function getAllIcons() {
    const icons = [];
    const iconLinks = document.querySelectorAll('link[rel*="icon"]');

    iconLinks.forEach((link) => {
      const href = link.getAttribute("href");
      if (href) {
        icons.push({
          rel: link.getAttribute("rel"),
          url: new URL(href, window.location.href).href,
          sizes: link.getAttribute("sizes") || "",
          type: link.getAttribute("type") || "",
        });
      }
    });

    return icons;
  }

  /**
   * 获取DOCTYPE信息
   */
  function getDoctypeInfo() {
    const doctype = document.doctype;
    if (doctype) {
      return {
        name: doctype.name,
        publicId: doctype.publicId,
        systemId: doctype.systemId,
      };
    }
    return null;
  }

  /**
   * 获取结构化数据
   */
  function getStructuredData() {
    const scripts = document.querySelectorAll(
      'script[type="application/ld+json"]'
    );
    const structuredData = [];

    scripts.forEach((script) => {
      try {
        const data = JSON.parse(script.textContent);
        structuredData.push(data);
      } catch (error) {
        console.log("Error parsing structured data:", error);
      }
    });

    return structuredData;
  }

  /**
   * 获取页面统计信息
   */
  function getPageStats() {
    return {
      totalElements: document.querySelectorAll("*").length,
      totalImages: document.querySelectorAll("img").length,
      totalLinks: document.querySelectorAll("a").length,
      totalScripts: document.querySelectorAll("script").length,
      totalStylesheets: document.querySelectorAll('link[rel="stylesheet"]')
        .length,
      headings: {
        h1: document.querySelectorAll("h1").length,
        h2: document.querySelectorAll("h2").length,
        h3: document.querySelectorAll("h3").length,
        h4: document.querySelectorAll("h4").length,
        h5: document.querySelectorAll("h5").length,
        h6: document.querySelectorAll("h6").length,
      },
    };
  }

  /**
   * 获取所有meta标签
   */
  function getAllMetaTags() {
    const metaTags = document.querySelectorAll("meta");
    const allMeta = [];

    metaTags.forEach((meta) => {
      const metaInfo = {};

      // 获取所有属性
      Array.from(meta.attributes).forEach((attr) => {
        metaInfo[attr.name] = attr.value;
      });

      allMeta.push(metaInfo);
    });

    return allMeta;
  }
}

// 页面加载完成后自动发送页面信息（可选）
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", () => {
    console.log("WebVault: DOM loaded");
  });
} else {
  console.log("WebVault: DOM already loaded");
}
