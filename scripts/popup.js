/**
 * WebVault Popup Script
 * 处理弹窗页面的交互
 */

// DOM元素
let elements = {};

// 初始化
document.addEventListener("DOMContentLoaded", () => {
  console.log("Popup loaded");
  initializeElements();
  attachEventListeners();
  loadBasicPageInfo();
});

/**
 * 初始化DOM元素引用
 */
function initializeElements() {
  elements = {
    // 基本信息
    pageTitle: document.getElementById("page-title"),
    pageUrl: document.getElementById("page-url"),
    pageDescription: document.getElementById("page-description"),

    // 截图相关
    screenshotPlaceholder: document.getElementById("screenshot-placeholder"),
    screenshot: document.getElementById("screenshot"),
    screenshotProgress: document.getElementById("screenshot-progress"),

    // 按钮
    sidepanelBtn: document.getElementById("sidepanel-btn"),
    exportBtn: document.getElementById("export-btn"),
    refreshBtn: document.getElementById("refresh-btn"),
    saveBtn: document.getElementById("save-btn"),

    // 通知
    notification: document.getElementById("notification"),
  };
}

/**
 * 绑定事件监听器
 */
function attachEventListeners() {
  // 打开侧边栏按钮
  if (elements.sidepanelBtn) {
    elements.sidepanelBtn.addEventListener("click", openSidePanel);
  }

  // 导出数据按钮
  if (elements.exportBtn) {
    elements.exportBtn.addEventListener("click", exportData);
  }

  // 刷新按钮
  if (elements.refreshBtn) {
    elements.refreshBtn.addEventListener("click", handleRefresh);
  }

  // 保存按钮
  if (elements.saveBtn) {
    elements.saveBtn.addEventListener("click", handleSave);
  }

  // 截图点击
  if (elements.screenshotPlaceholder) {
    elements.screenshotPlaceholder.addEventListener("click", captureScreenshot);
  }
}

/**
 * 打开侧边栏
 */
async function openSidePanel() {
  try {
    // 获取当前标签页
    const [tab] = await chrome.tabs.query({
      active: true,
      currentWindow: true,
    });

    if (tab && tab.id) {
      // 打开侧边栏
      await chrome.sidePanel.open({ tabId: tab.id });
      // 关闭弹窗
      window.close();
    }
  } catch (error) {
    console.error("Error opening side panel:", error);
    showNotification("无法打开侧边栏", "error");
  }
}

/**
 * 导出数据
 */
async function exportData() {
  try {
    // 获取存储的数据
    const data = await chrome.storage.local.get();
    const webVaultData = {};

    // 筛选WebVault数据
    Object.keys(data).forEach((key) => {
      if (key.startsWith("webvault_")) {
        webVaultData[key] = data[key];
      }
    });

    if (Object.keys(webVaultData).length === 0) {
      showNotification("没有数据可导出", "warning");
      return;
    }

    // 创建下载
    const blob = new Blob([JSON.stringify(webVaultData, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);

    // 使用downloads API下载文件
    await chrome.downloads.download({
      url: url,
      filename: `webvault_export_${
        new Date().toISOString().split("T")[0]
      }.json`,
    });

    showNotification("数据导出成功！", "success");
  } catch (error) {
    console.error("Error exporting data:", error);
    showNotification("导出失败: " + error.message, "error");
  }
}

/**
 * 加载基本页面信息
 */
async function loadBasicPageInfo() {
  try {
    // 向background脚本发送消息获取页面信息
    const response = await sendMessageToBackground({ action: "getPageInfo" });

    if (response.error) {
      throw new Error(response.error);
    }

    if (response.success && response.data) {
      updateBasicUI(response.data);
    }
  } catch (error) {
    console.error("Error loading basic page info:", error);
    showError("无法获取页面信息");
  }
}

/**
 * 更新基本UI显示
 */
function updateBasicUI(data) {
  try {
    // 基本信息
    updateTextContent(elements.pageTitle, data.title || "无标题");
    updateTextContent(elements.pageUrl, truncateUrl(data.url || ""));
    updateTextContent(
      elements.pageDescription,
      truncateText(data.description || "无描述", 100)
    );
  } catch (error) {
    console.error("Error updating basic UI:", error);
  }
}

/**
 * 处理刷新按钮点击
 */
async function handleRefresh() {
  try {
    setLoadingState(elements.refreshBtn, true);
    await loadBasicPageInfo();
    showNotification("页面信息已刷新！", "success");
  } catch (error) {
    console.error("Error during refresh:", error);
    showNotification("刷新失败: " + error.message, "error");
  } finally {
    setLoadingState(elements.refreshBtn, false);
  }
}

/**
 * 处理保存按钮
 */
async function handleSave() {
  try {
    setLoadingState(elements.saveBtn, true);

    // 获取完整页面信息
    const response = await sendMessageToBackground({ action: "getPageInfo" });

    if (response.error) {
      throw new Error(response.error);
    }

    if (response.success && response.data) {
      // 保存到本地存储
      await chrome.storage.local.set({
        [`webvault_${Date.now()}`]: response.data,
      });

      showNotification("数据已保存！", "success");
    }
  } catch (error) {
    console.error("Error saving data:", error);
    showNotification("保存失败: " + error.message, "error");
  } finally {
    setLoadingState(elements.saveBtn, false);
  }
}

/**
 * 截取屏幕截图
 */
async function captureScreenshot() {
  try {
    console.log("Capturing screenshot from popup...");
    showProgress(0);

    const response = await sendMessageToBackground({
      action: "captureScreenshot",
    });

    if (response.error) {
      throw new Error(response.error);
    }

    if (response.success && response.screenshot) {
      elements.screenshot.src = response.screenshot;
      elements.screenshot.style.display = "block";
      elements.screenshotPlaceholder.style.display = "none";
      showProgress(100);

      setTimeout(() => {
        showProgress(0);
      }, 1000);

      console.log("Screenshot captured successfully in popup");
    }
  } catch (error) {
    console.error("Error capturing screenshot:", error);
    showNotification("截图失败: " + error.message, "error");
  }
}

/**
 * 更新文本内容
 */
function updateTextContent(element, text) {
  if (element) {
    element.textContent = text;
    element.title = text; // 添加悬停提示
  }
}

/**
 * 截断URL显示
 */
function truncateUrl(url) {
  if (url.length > 40) {
    return url.substring(0, 37) + "...";
  }
  return url;
}

/**
 * 截断文本
 */
function truncateText(text, maxLength) {
  if (text.length > maxLength) {
    return text.substring(0, maxLength - 3) + "...";
  }
  return text;
}

/**
 * 设置按钮加载状态
 */
function setLoadingState(button, loading) {
  if (button) {
    button.disabled = loading;
    const icon = button.querySelector("i");
    if (icon) {
      if (loading) {
        icon.classList.add("fa-spin");
      } else {
        icon.classList.remove("fa-spin");
      }
    }
  }
}

/**
 * 显示进度
 */
function showProgress(percent) {
  if (elements.screenshotProgress) {
    elements.screenshotProgress.style.width = percent + "%";
  }
}

/**
 * 显示通知
 */
function showNotification(message, type = "info") {
  if (elements.notification) {
    elements.notification.textContent = message;
    elements.notification.className = `notification ${type}`;
    elements.notification.style.display = "block";

    setTimeout(() => {
      elements.notification.style.display = "none";
    }, 3000);
  }
}

/**
 * 显示错误
 */
function showError(message) {
  showNotification(message, "error");

  // 更新UI显示错误状态
  const errorElements = [
    elements.pageTitle,
    elements.pageUrl,
    elements.pageDescription,
  ];

  errorElements.forEach((element) => {
    if (element) {
      element.textContent = "加载失败";
    }
  });
}

/**
 * 发送消息到background脚本
 */
function sendMessageToBackground(message) {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage(message, resolve);
  });
}
