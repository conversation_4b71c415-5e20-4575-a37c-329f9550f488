/**
 * WebVault Background Script
 * 处理扩展的后台服务和消息传递
 */

// 扩展安装或启动时的初始化
chrome.runtime.onInstalled.addListener(() => {
  console.log("WebVault extension installed");
});

// 监听来自popup和sidepanel的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log("Background received message:", request);

  switch (request.action) {
    case "getPageInfo":
      handleGetPageInfo(request, sender, sendResponse);
      return true; // 保持消息通道开启以进行异步响应

    case "captureScreenshot":
      handleCaptureScreenshot(request, sender, sendResponse);
      return true;

    case "injectContentScript":
      handleInjectContentScript(request, sender, sendResponse);
      return true;

    default:
      sendResponse({ error: "Unknown action" });
  }
});

/**
 * 获取页面信息
 */
async function handleGetPageInfo(request, sender, sendResponse) {
  try {
    const [tab] = await chrome.tabs.query({
      active: true,
      currentWindow: true,
    });

    if (!tab || !tab.id) {
      sendResponse({ error: "无法获取当前标签页" });
      return;
    }

    // 注入内容脚本和标签分析器
    try {
      await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        files: [
          "scripts/tagConfig.js",
          "scripts/tagAnalyzer.js",
          "scripts/advancedTagAnalyzer.js",
          "scripts/content.js"
        ],
      });
    } catch (error) {
      console.log("Content script already injected or error:", error);
    }

    // 向内容脚本发送消息获取页面信息
    chrome.tabs.sendMessage(tab.id, { action: "getPageInfo" }, (response) => {
      if (chrome.runtime.lastError) {
        sendResponse({
          error: "无法获取页面信息: " + chrome.runtime.lastError.message,
        });
      } else {
        sendResponse(response);
      }
    });
  } catch (error) {
    console.error("Error in handleGetPageInfo:", error);
    sendResponse({ error: error.message });
  }
}

/**
 * 截取屏幕截图
 */
async function handleCaptureScreenshot(request, sender, sendResponse) {
  try {
    const [tab] = await chrome.tabs.query({
      active: true,
      currentWindow: true,
    });

    if (!tab || !tab.id) {
      sendResponse({ error: "无法获取当前标签页" });
      return;
    }

    console.log("开始截图流程...");

    // 确保content script已注入
    try {
      await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        files: [
          "scripts/tagConfig.js",
          "scripts/tagAnalyzer.js",
          "scripts/advancedTagAnalyzer.js",
          "scripts/content.js"
        ],
      });
    } catch (error) {
      console.log("Content script注入失败或已存在:", error);
    }

    // 尝试获取完整页面截图
    let screenshotResult;

    try {
      // 使用content script获取完整页面截图
      screenshotResult = await chrome.tabs.sendMessage(tab.id, {
        action: "captureFullPage"
      });

      console.log("Content script截图结果:", screenshotResult);

      if (!screenshotResult || !screenshotResult.success) {
        throw new Error(`Content script截图失败: ${screenshotResult?.error || '未知错误'}`);
      }
    } catch (error) {
      console.log("完整页面截图失败，使用可见区域截图:", error);

      // 降级到可见区域截图
      try {
        const dataUrl = await chrome.tabs.captureVisibleTab(tab.windowId, {
          format: "png",
          quality: 90,
        });

        screenshotResult = {
          success: true,
          screenshot: dataUrl,
          method: 'visible-area-fallback',
          dimensions: { width: 'unknown', height: 'unknown' }
        };
      } catch (captureError) {
        throw new Error(`所有截图方法都失败: ${captureError.message}`);
      }
    }

    console.log(`截图完成，方法: ${screenshotResult.method}`);

    sendResponse({
      success: true,
      screenshot: screenshotResult.screenshot,
      timestamp: Date.now(),
      method: screenshotResult.method || 'unknown',
      dimensions: screenshotResult.dimensions
    });
  } catch (error) {
    console.error("Error in handleCaptureScreenshot:", error);
    sendResponse({ error: error.message });
  }
}

/**
 * 注入内容脚本
 */
async function handleInjectContentScript(request, sender, sendResponse) {
  try {
    const [tab] = await chrome.tabs.query({
      active: true,
      currentWindow: true,
    });

    if (!tab || !tab.id) {
      sendResponse({ error: "无法获取当前标签页" });
      return;
    }

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      files: ["scripts/content.js"],
    });

    sendResponse({ success: true });
  } catch (error) {
    console.error("Error in handleInjectContentScript:", error);
    sendResponse({ error: error.message });
  }
}

// 标签页更新时的处理
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  // 当页面完成加载时，可以进行一些初始化操作
  if (changeInfo.status === "complete" && tab.url) {
    console.log("Tab updated:", tab.url);
  }
});

// 监听扩展图标点击事件
chrome.action.onClicked.addListener((tab) => {
  console.log("Extension icon clicked for tab:", tab.id);
});
