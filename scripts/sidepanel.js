/**
 * WebVault Sidepanel Script
 * 处理侧边栏的交互和数据显示
 */

// DOM元素
let elements = {};

// 页面数据
let currentPageData = null;

// 初始化
document.addEventListener("DOMContentLoaded", () => {
  console.log("Sidepanel loaded");
  initializeElements();
  attachEventListeners();
  loadPageInfo();
});

/**
 * 初始化DOM元素引用
 */
function initializeElements() {
  elements = {
    // 基本信息
    pageTitle: document.getElementById("page-title"),
    pageUrl: document.getElementById("page-url"),
    pageDescription: document.getElementById("page-description"),
    pageCharset: document.getElementById("page-charset"),
    pageKeywords: document.getElementById("page-keywords"),
    pageAuthor: document.getElementById("page-author"),

    // 智能标签
    smartTags: document.getElementById("smart-tags"),
    suggestedTags: document.getElementById("suggested-tags"),

    // 截图相关
    screenshotPlaceholder: document.getElementById("screenshot-placeholder"),
    screenshot: document.getElementById("screenshot"),
    screenshotProgress: document.getElementById("screenshot-progress"),

    // 图标相关
    faviconImg: document.getElementById("favicon-img"),
    faviconUrl: document.getElementById("favicon-url"),
    faviconSize: document.getElementById("favicon-size"),
    appleTouchImg: document.getElementById("apple-touch-img"),
    appleTouchUrl: document.getElementById("apple-touch-url"),
    appleTouchSize: document.getElementById("apple-touch-size"),
    iconGrid: document.querySelector(".icon-grid"),

    // 元数据容器
    metadataContainer: document.getElementById("metadata-container"),

    // 按钮
    refreshBtn: document.getElementById("refresh-btn"),
    configBtn: document.getElementById("config-btn"),
    saveBtn: document.getElementById("save-btn"),

    // 标签页
    tabs: document.querySelectorAll(".tab"),
    tabContents: document.querySelectorAll(".tab-content"),

    // 通知
    notification: document.getElementById("notification"),
  };
}

/**
 * 绑定事件监听器
 */
function attachEventListeners() {
  // 重新采集按钮
  if (elements.refreshBtn) {
    elements.refreshBtn.addEventListener("click", handleRefresh);
  }

  // 配置按钮
  if (elements.configBtn) {
    elements.configBtn.addEventListener("click", openConfigPage);
  }

  // 保存按钮
  if (elements.saveBtn) {
    elements.saveBtn.addEventListener("click", handleSave);
  }

  // 截图点击
  if (elements.screenshotPlaceholder) {
    elements.screenshotPlaceholder.addEventListener("click", captureScreenshot);
  }

  // 标签页切换
  elements.tabs.forEach((tab) => {
    tab.addEventListener("click", () => switchTab(tab.dataset.tab));
  });
}

/**
 * 更新元数据显示为JSON请求数据
 */
function updateMetadata(data) {
  if (!elements.metadataContainer) return;

  // 构建后端请求数据
  const requestData = buildRequestData(data);

  // 存储当前的请求数据
  window.currentRequestData = requestData;

  // 生成语法高亮的JSON
  const highlightedJson = highlightJson(JSON.stringify(requestData, null, 2));

  // 渲染JSON数据和操作按钮
  elements.metadataContainer.innerHTML = `
    <div class="json-container">
      <div class="json-header">
        <div class="json-title">request-data.json</div>
        <div class="json-actions">
          <button class="btn-small" id="copy-json-btn" title="复制JSON">
            <i class="fas fa-copy"></i>
          </button>
          <button class="btn-small" id="download-json-btn" title="下载JSON">
            <i class="fas fa-download"></i>
          </button>
        </div>
      </div>
      <pre class="json-content" id="json-content">${highlightedJson}</pre>
    </div>
  `;

  // 绑定新按钮的事件
  const copyBtn = document.getElementById("copy-json-btn");
  const downloadBtn = document.getElementById("download-json-btn");

  if (copyBtn) {
    copyBtn.addEventListener("click", copyJsonToClipboard);
  }

  if (downloadBtn) {
    downloadBtn.addEventListener("click", downloadJsonFile);
  }
}

/**
 * JSON语法高亮
 */
function highlightJson(json) {
  return json
    .replace(
      /("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,
      function (match) {
        let cls = "json-number";
        if (/^"/.test(match)) {
          if (/:$/.test(match)) {
            cls = "json-key";
          } else {
            cls = "json-string";
          }
        } else if (/true|false/.test(match)) {
          cls = "json-boolean";
        } else if (/null/.test(match)) {
          cls = "json-null";
        }
        return '<span class="' + cls + '">' + match + "</span>";
      }
    )
    .replace(/([{}[\],])/g, '<span class="json-punctuation">$1</span>');
}

/**
 * 处理重新采集按钮点击
 */
async function handleRefresh() {
  console.log("Refresh button clicked - starting page info collection");

  try {
    // 显示加载状态
    setLoadingState(true);
    showNotification("正在重新采集页面信息...", "info");

    // 获取页面信息
    await loadPageInfo();

    // 自动截图
    await captureScreenshot();

    showNotification("页面信息重新采集完成！", "success");
  } catch (error) {
    console.error("Error during refresh:", error);
    showNotification("采集失败: " + error.message, "error");
  } finally {
    setLoadingState(false);
  }
}

/**
 * 加载页面信息
 */
async function loadPageInfo() {
  try {
    console.log("Loading page info...");

    // 设置智能标签加载状态
    if (elements.smartTags) {
      elements.smartTags.innerHTML = '<div class="tags-loading"><i class="fas fa-spinner fa-spin"></i> 智能分析中...</div>';
    }
    if (elements.suggestedTags) {
      elements.suggestedTags.innerHTML = '<div class="tags-loading"><i class="fas fa-spinner fa-spin"></i> 分析中...</div>';
    }

    // 向background脚本发送消息获取页面信息
    const response = await sendMessageToBackground({ action: "getPageInfo" });

    if (response.error) {
      throw new Error(response.error);
    }

    if (response.success && response.data) {
      currentPageData = response.data;
      updateUI(response.data);
      console.log("Page info loaded successfully:", response.data);

      // 特别输出智能标签信息用于调试
      if (response.data.suggestedTags) {
        console.log("智能标签分析结果:", response.data.suggestedTags);
      } else {
        console.log("未获取到智能标签数据");
      }
    } else {
      throw new Error("No data received");
    }
  } catch (error) {
    console.error("Error loading page info:", error);
    showError("无法获取页面信息: " + error.message);

    // 设置标签错误状态
    if (elements.smartTags) {
      elements.smartTags.innerHTML = '<div class="tags-error">标签分析失败</div>';
    }
    if (elements.suggestedTags) {
      elements.suggestedTags.innerHTML = '<div class="tags-error">分析失败</div>';
    }
  }
}

/**
 * 更新UI显示
 */
function updateUI(data) {
  try {
    // 基本信息
    updateTextContent(elements.pageTitle, data.title || "无标题");
    updateTextContent(elements.pageUrl, data.url || "");
    updateTextContent(elements.pageDescription, data.description || "无描述");
    updateTextContent(elements.pageCharset, data.charset || "未知");
    updateTextContent(elements.pageKeywords, data.keywords || "无关键词");
    updateTextContent(elements.pageAuthor, data.author || "未知作者");

    // 更新智能标签
    updateSmartTags(data.suggestedTags || []);

    // 更新网站图标
    updateFavicon(data.favicon);
    updateAppleTouchIcon(data.appleTouchIcon);
    updateAllIcons(data.allIcons || []);

    // 更新元数据
    updateMetadata(data);
  } catch (error) {
    console.error("Error updating UI:", error);
  }
}

/**
 * 更新文本内容
 */
function updateTextContent(element, text) {
  if (element) {
    element.textContent = text;
    element.title = text; // 添加悬停提示
  }
}

/**
 * 更新智能标签显示
 */
function updateSmartTags(suggestedTags) {
  console.log("更新智能标签:", suggestedTags);

  // 更新头部的智能标签区域
  if (elements.smartTags) {
    if (!suggestedTags || suggestedTags.length === 0) {
      elements.smartTags.innerHTML = '<div class="tags-empty">暂无智能标签</div>';
    } else {
      const tagsContainer = document.createElement('div');
      tagsContainer.className = 'tags-container';

      suggestedTags.slice(0, 6).forEach(tagInfo => {
        const tagElement = document.createElement('span');
        tagElement.className = `smart-tag ${tagInfo.confidence}-confidence`;
        tagElement.innerHTML = `
          ${tagInfo.tag}
          <span class="tag-score">${tagInfo.score}</span>
        `;
        tagElement.title = `标签: ${tagInfo.tag}\n分数: ${tagInfo.score}\n置信度: ${tagInfo.confidence}`;
        tagsContainer.appendChild(tagElement);
      });

      elements.smartTags.innerHTML = '';
      elements.smartTags.appendChild(tagsContainer);
    }
  }

  // 更新基本信息中的智能标签行
  if (elements.suggestedTags) {
    if (!suggestedTags || suggestedTags.length === 0) {
      elements.suggestedTags.innerHTML = '<span class="tags-empty">暂无智能标签</span>';
    } else {
      const tagsContainer = document.createElement('div');
      tagsContainer.className = 'tags-container';

      suggestedTags.forEach(tagInfo => {
        const tagElement = document.createElement('span');
        tagElement.className = `smart-tag ${tagInfo.confidence}-confidence`;
        tagElement.innerHTML = `
          ${tagInfo.tag}
          <span class="tag-score">${tagInfo.score}</span>
        `;
        tagElement.title = `标签: ${tagInfo.tag}\n分数: ${tagInfo.score}\n置信度: ${tagInfo.confidence}`;
        tagsContainer.appendChild(tagElement);
      });

      elements.suggestedTags.innerHTML = '';
      elements.suggestedTags.appendChild(tagsContainer);
    }
  }
}

/**
 * 更新网站图标
 */
function updateFavicon(favicon) {
  if (
    favicon &&
    elements.faviconImg &&
    elements.faviconUrl &&
    elements.faviconSize
  ) {
    elements.faviconImg.src = favicon.url;
    elements.faviconImg.style.display = "block";
    elements.faviconUrl.textContent = favicon.url;
    elements.faviconSize.textContent = favicon.sizes || "未知";

    // 处理图片加载错误
    elements.faviconImg.onerror = () => {
      elements.faviconImg.style.display = "none";
    };
  }
}

/**
 * 更新Apple Touch图标
 */
function updateAppleTouchIcon(appleTouchIcon) {
  if (
    appleTouchIcon &&
    elements.appleTouchImg &&
    elements.appleTouchUrl &&
    elements.appleTouchSize
  ) {
    elements.appleTouchImg.src = appleTouchIcon.url;
    elements.appleTouchImg.style.display = "block";
    elements.appleTouchUrl.textContent = appleTouchIcon.url;
    elements.appleTouchSize.textContent = appleTouchIcon.sizes || "未知";

    elements.appleTouchImg.onerror = () => {
      elements.appleTouchImg.style.display = "none";
    };
  } else if (elements.appleTouchUrl) {
    elements.appleTouchUrl.textContent = "未找到Apple Touch图标";
  }
}

/**
 * 更新所有图标
 */
function updateAllIcons(icons) {
  if (!elements.iconGrid) return;

  elements.iconGrid.innerHTML = "";

  if (icons.length === 0) {
    elements.iconGrid.innerHTML = "<p>未找到其他图标</p>";
    return;
  }

  icons.forEach((icon) => {
    const iconDiv = document.createElement("div");
    iconDiv.className = "icon-item";
    iconDiv.innerHTML = `
      <div class="icon-preview">
        <img src="${icon.url}" alt="${
      icon.rel
    }" onerror="this.style.display='none'">
      </div>
      <div class="icon-info">
        <div><strong>类型:</strong> ${icon.rel || "未知"}</div>
        <div><strong>尺寸:</strong> ${icon.sizes || "未知"}</div>
        <div><strong>格式:</strong> ${icon.type || "未知"}</div>
      </div>
    `;
    elements.iconGrid.appendChild(iconDiv);
  });
}

/**
 * 构建发送到后端的请求数据
 */
function buildRequestData(data) {
  const requestData = {
    // 基本信息
    basicInfo: {
      title: data.title || "",
      url: data.url || "",
      description: data.description || "",
      keywords: data.keywords || "",
      author: data.author || "",
      charset: data.charset || "UTF-8",
      viewport: data.viewport || "",
      generator: data.generator || "",
      collectedAt: data.collectedAt || new Date().toISOString(),
    },

    // 智能标签
    tags: data.suggestedTags && data.suggestedTags.length > 0
      ? data.suggestedTags.map(tagInfo => ({
          name: tagInfo.tag,
          score: tagInfo.score,
          confidence: tagInfo.confidence,
          source: tagInfo.sources || ['auto-analysis']
        }))
      : [],

    // 网页截图（Base64格式）
    screenshot: {
      dataUrl:
        elements.screenshot && elements.screenshot.src
          ? elements.screenshot.src
          : null,
      capturedAt: new Date().toISOString(),
      format: "png",
    },

    // 网站主图标
    favicon: data.favicon
      ? {
          url: data.favicon.url,
          sizes: data.favicon.sizes || "",
          type: data.favicon.type || "image/x-icon",
        }
      : null,

    // Open Graph数据
    openGraph:
      data.openGraph && Object.keys(data.openGraph).length > 0
        ? data.openGraph
        : null,

    // Twitter Card数据
    twitterCard:
      data.twitterCard && Object.keys(data.twitterCard).length > 0
        ? data.twitterCard
        : null,

    // Apple Touch图标
    appleTouchIcon: data.appleTouchIcon
      ? {
          url: data.appleTouchIcon.url,
          sizes: data.appleTouchIcon.sizes || "",
          type: data.appleTouchIcon.type || "",
        }
      : null,

    // 页面统计
    stats: data.stats || null,

    // 结构化数据
    structuredData:
      data.structuredData && data.structuredData.length > 0
        ? data.structuredData
        : null,

    // DOCTYPE信息
    doctype: data.doctype || null,

    // 请求元信息
    requestInfo: {
      source: "WebVault Chrome Extension",
      version: "1.0.0",
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
    },
  };

  return requestData;
}

/**
 * 复制JSON到剪贴板
 */
async function copyJsonToClipboard() {
  const copyBtn = document.getElementById("copy-json-btn");

  try {
    if (!window.currentRequestData) {
      showNotification("没有数据可复制", "warning");
      return;
    }

    const jsonString = JSON.stringify(window.currentRequestData, null, 2);

    // 显示复制中状态
    if (copyBtn) {
      copyBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
      copyBtn.disabled = true;
      copyBtn.title = "复制中...";
    }

    // 使用现代的Clipboard API
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(jsonString);
    } else {
      // 降级方案：使用传统方法
      const textArea = document.createElement("textarea");
      textArea.value = jsonString;
      textArea.style.position = "fixed";
      textArea.style.opacity = "0";
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand("copy");
      document.body.removeChild(textArea);
    }

    // 显示成功状态
    if (copyBtn) {
      copyBtn.innerHTML = '<i class="fas fa-check"></i>';
      copyBtn.classList.add("copy-success");
      copyBtn.title = "已复制！";

      setTimeout(() => {
        copyBtn.innerHTML = '<i class="fas fa-copy"></i>';
        copyBtn.classList.remove("copy-success");
        copyBtn.disabled = false;
        copyBtn.title = "复制JSON";
      }, 2000);
    }

    showNotification("JSON数据已复制到剪贴板！", "success");
  } catch (error) {
    console.error("Error copying to clipboard:", error);
    showNotification("复制失败: " + error.message, "error");

    // 恢复按钮状态
    if (copyBtn) {
      copyBtn.innerHTML = '<i class="fas fa-copy"></i>';
      copyBtn.disabled = false;
      copyBtn.title = "复制JSON";
    }
  }
}

/**
 * 下载JSON文件
 */
function downloadJsonFile() {
  try {
    if (!window.currentRequestData) {
      showNotification("没有数据可下载", "warning");
      return;
    }

    const jsonString = JSON.stringify(window.currentRequestData, null, 2);
    const blob = new Blob([jsonString], { type: "application/json" });
    const url = URL.createObjectURL(blob);

    // 创建下载链接
    const link = document.createElement("a");
    link.href = url;
    link.download = `webvault_request_data_${new Date()
      .toISOString()
      .slice(0, 19)
      .replace(/:/g, "-")}.json`;

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理URL对象
    URL.revokeObjectURL(url);

    showNotification("JSON文件下载已开始！", "success");
  } catch (error) {
    console.error("Error downloading JSON:", error);
    showNotification("下载失败: " + error.message, "error");
  }
}

/**
 * 截取屏幕截图
 */
async function captureScreenshot() {
  try {
    console.log("Capturing screenshot...");
    showProgress(0);

    const response = await sendMessageToBackground({
      action: "captureScreenshot",
    });

    if (response.error) {
      throw new Error(response.error);
    }

    if (response.success && response.screenshot) {
      elements.screenshot.src = response.screenshot;
      elements.screenshot.style.display = "block";
      elements.screenshotPlaceholder.style.display = "none";
      showProgress(100);

      // 截图完成后更新JSON数据
      if (currentPageData) {
        updateMetadata(currentPageData);
      }

      setTimeout(() => {
        showProgress(0);
      }, 1000);

      console.log("Screenshot captured successfully");
    }
  } catch (error) {
    console.error("Error capturing screenshot:", error);
    showNotification("截图失败: " + error.message, "error");
  }
}

/**
 * 处理保存按钮
 */
async function handleSave() {
  if (!currentPageData) {
    showNotification("没有数据可保存", "warning");
    return;
  }

  try {
    // 显示保存中状态
    const originalText = elements.saveBtn.innerHTML;
    elements.saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
    elements.saveBtn.disabled = true;

    // 获取配置
    const config = await chrome.storage.sync.get({
      apiUrl: '',
      apiKey: '',
      timeout: 30
    });

    if (!config.apiUrl) {
      // 如果没有配置API，保存到本地存储
      await chrome.storage.local.set({
        [`webvault_${Date.now()}`]: currentPageData,
      });
      showNotification("API未配置，已保存到本地存储", "warning");
      return;
    }

    // 构建请求数据
    const requestData = buildRequestData(currentPageData);

    console.log("发送到API的数据:", requestData);

    // 准备请求头
    const headers = {
      'Content-Type': 'application/json'
    };

    if (config.apiKey) {
      headers['Authorization'] = `Bearer ${config.apiKey}`;
    }

    // 设置超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout * 1000);

    // 发送POST请求
    const response = await fetch(config.apiUrl, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestData),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log("API响应:", result);

    // 同时保存到本地存储作为备份
    await chrome.storage.local.set({
      [`webvault_${Date.now()}`]: {
        ...currentPageData,
        savedToApi: true,
        apiResponse: result
      },
    });

    showNotification("数据已成功保存到资源库！", "success");

  } catch (error) {
    console.error("Error saving data:", error);

    let errorMessage = "保存失败: ";
    if (error.name === 'AbortError') {
      errorMessage += "请求超时";
    } else if (error.message.includes('Failed to fetch')) {
      errorMessage += "网络错误，请检查API地址和网络连接";
    } else {
      errorMessage += error.message;
    }

    // 如果API失败，尝试保存到本地存储
    try {
      await chrome.storage.local.set({
        [`webvault_${Date.now()}`]: {
          ...currentPageData,
          savedToApi: false,
          error: error.message
        },
      });
      errorMessage += "，已保存到本地存储";
    } catch (localError) {
      console.error("Local storage also failed:", localError);
    }

    showNotification(errorMessage, "error");
  } finally {
    // 恢复按钮状态
    elements.saveBtn.innerHTML = originalText;
    elements.saveBtn.disabled = false;
  }
}

/**
 * 打开配置页面
 */
function openConfigPage() {
  chrome.runtime.openOptionsPage();
}

/**
 * 切换标签页
 */
function switchTab(tabName) {
  // 移除所有活动状态
  elements.tabs.forEach((tab) => tab.classList.remove("active"));
  elements.tabContents.forEach((content) => content.classList.remove("active"));

  // 激活当前标签页
  const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
  const activeContent = document.getElementById(`${tabName}-tab`);

  if (activeTab) activeTab.classList.add("active");
  if (activeContent) activeContent.classList.add("active");
}

/**
 * 显示加载状态
 */
function setLoadingState(loading) {
  if (elements.refreshBtn) {
    elements.refreshBtn.disabled = loading;
    const icon = elements.refreshBtn.querySelector("i");
    if (icon) {
      if (loading) {
        icon.classList.add("fa-spin");
      } else {
        icon.classList.remove("fa-spin");
      }
    }
  }
}

/**
 * 显示进度
 */
function showProgress(percent) {
  if (elements.screenshotProgress) {
    elements.screenshotProgress.style.width = percent + "%";
  }
}

/**
 * 显示通知
 */
function showNotification(message, type = "info") {
  if (elements.notification) {
    elements.notification.textContent = message;
    elements.notification.className = `notification ${type}`;
    elements.notification.style.display = "block";

    setTimeout(() => {
      elements.notification.style.display = "none";
    }, 3000);
  }
}

/**
 * 显示错误
 */
function showError(message) {
  showNotification(message, "error");

  // 更新UI显示错误状态
  const errorElements = [
    elements.pageTitle,
    elements.pageUrl,
    elements.pageDescription,
    elements.pageCharset,
    elements.pageKeywords,
    elements.pageAuthor,
  ];

  errorElements.forEach((element) => {
    if (element) {
      element.textContent = "加载失败";
    }
  });
}

/**
 * 发送消息到background脚本
 */
function sendMessageToBackground(message) {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage(message, resolve);
  });
}
