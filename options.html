<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebVault 配置</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #4361ee;
            --primary-dark: #3a56d4;
            --secondary: #06d6a0;
            --dark: #1e1e2c;
            --darker: #161622;
            --light: #f8f9fa;
            --gray: #6c757d;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Segoe UI", "Noto Sans SC", Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, var(--darker), var(--dark));
            color: var(--light);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            background: linear-gradient(to right, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            color: var(--gray);
            font-size: 1.1em;
        }

        .config-section {
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .config-section h3 {
            color: var(--primary);
            margin-bottom: 20px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--light);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
            color: var(--light);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .form-help {
            font-size: 12px;
            color: var(--gray);
            margin-top: 5px;
        }

        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(67, 97, 238, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: var(--light);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        .btn-test {
            background: linear-gradient(135deg, var(--secondary), #05b894);
            color: white;
        }

        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(6, 214, 160, 0.3);
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-top: 10px;
        }

        .status-success {
            background: rgba(6, 214, 160, 0.2);
            color: var(--success);
            border: 1px solid rgba(6, 214, 160, 0.3);
        }

        .status-error {
            background: rgba(239, 71, 111, 0.2);
            color: var(--danger);
            border: 1px solid rgba(239, 71, 111, 0.3);
        }

        .status-warning {
            background: rgba(255, 209, 102, 0.2);
            color: var(--warning);
            border: 1px solid rgba(255, 209, 102, 0.3);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success);
        }

        .notification.error {
            background: var(--danger);
        }

        .notification.warning {
            background: var(--warning);
            color: var(--dark);
        }

        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .loading .btn {
            cursor: not-allowed;
        }

        @media (max-width: 600px) {
            .container {
                margin: 10px;
                padding: 20px;
            }

            .button-group {
                flex-direction: column;
            }

            .btn {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-cog"></i> WebVault 配置</h1>
            <p>配置后端API接口和插件设置</p>
        </div>

        <div class="config-section">
            <h3><i class="fas fa-server"></i> 后端API配置</h3>
            
            <div class="form-group">
                <label for="api-url">API接口地址</label>
                <input type="url" id="api-url" placeholder="https://your-api.com/api/webvault">
                <div class="form-help">输入完整的API接口地址，用于保存网页数据</div>
            </div>

            <div class="form-group">
                <label for="api-key">API密钥 (可选)</label>
                <input type="password" id="api-key" placeholder="输入API密钥">
                <div class="form-help">如果API需要认证，请输入密钥</div>
            </div>

            <div class="form-group">
                <label for="timeout">请求超时时间 (秒)</label>
                <input type="number" id="timeout" value="30" min="5" max="120">
                <div class="form-help">API请求的超时时间，建议30-60秒</div>
            </div>

            <button class="btn btn-test" id="test-connection">
                <i class="fas fa-plug"></i> 测试连接
            </button>

            <div id="test-result" class="test-result" style="display: none;"></div>
            <div id="connection-status"></div>
        </div>

        <div class="config-section">
            <h3><i class="fas fa-tags"></i> 标签分析配置</h3>
            
            <div class="form-group">
                <label for="max-tags">最大标签数量</label>
                <input type="number" id="max-tags" value="10" min="3" max="20">
                <div class="form-help">每个页面最多生成的标签数量</div>
            </div>

            <div class="form-group">
                <label for="min-confidence">最小置信度</label>
                <input type="number" id="min-confidence" value="0.3" min="0.1" max="1.0" step="0.1">
                <div class="form-help">只显示置信度高于此值的标签</div>
            </div>
        </div>

        <div class="button-group">
            <button class="btn btn-primary" id="save-config">
                <i class="fas fa-save"></i> 保存配置
            </button>
            <button class="btn btn-secondary" id="reset-config">
                <i class="fas fa-undo"></i> 重置默认
            </button>
        </div>
    </div>

    <div id="notification" class="notification"></div>

    <script src="scripts/options.js"></script>
</body>
</html>
