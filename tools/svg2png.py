from pathlib import Path
from typing import List, Optional

import cairosvg
import typer

app = typer.Typer()


@app.command()
def convert_svg(
    svg_file: Path = typer.Argument(
        ...,
        exists=True,
        file_okay=True,
        dir_okay=False,
        readable=True,
        help="Path to input SVG file",
    ),
    output_dir: Path = typer.Option(
        Path.cwd(),
        "--output-dir",
        "-o",
        file_okay=False,
        dir_okay=True,
        writable=True,
        help="Directory to save PNG files",
    ),
    sizes: Optional[List[int]] = typer.Option(
        None,
        "--size",
        "-s",
        help="List of output sizes (can be repeated). Default: 16,32,48,128",
    ),
):
    """
    Convert SVG logo to multiple PNG sizes
    """
    # 设置默认尺寸
    target_sizes = sizes or [16, 32, 48, 128]

    # 确保输出目录存在
    output_dir.mkdir(parents=True, exist_ok=True)

    typer.echo(f"Converting {svg_file} to PNG in sizes: {target_sizes}")

    # 处理每个尺寸
    for size in target_sizes:
        output_file = output_dir / f"icon-{size}.png"
        try:
            # 转换 SVG 到 PNG
            cairosvg.svg2png(
                url=str(svg_file),
                write_to=str(output_file),
                output_width=size,
                output_height=size,
            )
            typer.echo(f"✅ Created {output_file} ({size}x{size}px)")
        except Exception as e:
            typer.echo(f"❌ Error creating {size}px version: {str(e)}", err=True)


if __name__ == "__main__":
    app()
