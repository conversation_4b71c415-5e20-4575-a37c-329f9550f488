<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
  <defs>
    <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4361ee" />
      <stop offset="100%" stop-color="#06d6a0" />
    </linearGradient>
    
    <linearGradient id="innerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3a56d4" />
      <stop offset="100%" stop-color="#05b88f" />
    </linearGradient>
    
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3" result="blur" />
      <feFlood flood-color="#4361ee" flood-opacity="0.8" result="glowColor" />
      <feComposite in="glowColor" in2="blur" operator="in" result="glow" />
      <feBlend in="SourceGraphic" in2="glow" mode="screen" />
    </filter>
    
    <pattern id="gridPattern" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M20 0H0V20" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1" />
    </pattern>
    
    <pattern id="codePattern" width="30" height="30" patternUnits="userSpaceOnUse">
      <text x="5" y="15" font-family="monospace" font-size="10" fill="rgba(255,255,255,0.5)">&lt;/&gt;</text>
    </pattern>
  </defs>
  
  <!-- 外框 - 数据立方体 -->
  <g filter="url(#glow)">
    <path d="M50 50 L150 50 L180 80 L80 80 Z" fill="url(#mainGradient)" />
    <path d="M150 50 L180 80 L180 180 L150 150 Z" fill="url(#innerGradient)" />
    <path d="M50 50 L80 80 L80 180 L50 150 Z" fill="url(#innerGradient)" />
    <path d="M80 80 L180 80 L150 150 L50 150 Z" fill="url(#mainGradient)" />
    
    <!-- 网格纹理 -->
    <path d="M50 50 L150 50 L180 80 L80 80 Z" fill="url(#gridPattern)" opacity="0.4" />
    <path d="M80 80 L180 80 L150 150 L50 150 Z" fill="url(#gridPattern)" opacity="0.4" />
    
    <!-- 代码纹理 -->
    <path d="M150 50 L180 80 L180 180 L150 150 Z" fill="url(#codePattern)" opacity="0.3" />
    <path d="M50 50 L80 80 L80 180 L50 150 Z" fill="url(#codePattern)" opacity="0.3" />
  </g>
  
  <!-- 核心元素 - 网页与数据 -->
  <g transform="translate(65, 65)">
    <!-- 浏览器窗口 -->
    <rect x="0" y="0" width="70" height="70" rx="5" fill="#1e1e2c" stroke="#4361ee" stroke-width="2" />
    
    <!-- 地址栏 -->
    <rect x="5" y="5" width="60" height="10" rx="2" fill="#161622" />
    <rect x="5" y="5" width="40" height="10" rx="2" fill="#4361ee" opacity="0.7" />
    <circle cx="60" cy="10" r="2" fill="#06d6a0" />
    <circle cx="65" cy="10" r="2" fill="#ef476f" />
    
    <!-- 网页内容 -->
    <rect x="5" y="20" width="60" height="45" rx="2" fill="#161622" />
    
    <!-- 网页网格 -->
    <line x1="5" y1="30" x2="65" y2="30" stroke="rgba(255,255,255,0.1)" stroke-width="1" />
    <line x1="5" y1="40" x2="65" y2="40" stroke="rgba(255,255,255,0.1)" stroke-width="1" />
    <line x1="5" y1="50" x2="65" y2="50" stroke="rgba(255,255,255,0.1)" stroke-width="1" />
    <line x1="5" y1="60" x2="65" y2="60" stroke="rgba(255,255,255,0.1)" stroke-width="1" />
    
    <!-- 数据点 -->
    <circle cx="15" cy="35" r="3" fill="#06d6a0" />
    <circle cx="15" cy="45" r="3" fill="#4361ee" />
    <circle cx="15" cy="55" r="3" fill="#ffd166" />
    
    <!-- 数据流动 -->
    <path d="M25 35 L40 35" stroke="#06d6a0" stroke-width="2" />
    <path d="M25 45 L40 45" stroke="#4361ee" stroke-width="2" />
    <path d="M25 55 L40 55" stroke="#ffd166" stroke-width="2" />
    
    <!-- 数据库图标 -->
    <ellipse cx="50" cy="35" rx="8" ry="4" fill="#1e1e2c" stroke="#4361ee" stroke-width="1" />
    <ellipse cx="50" cy="35" rx="6" ry="2" fill="#161622" />
    <path d="M42 35 L42 55" stroke="#4361ee" stroke-width="1" />
    <path d="M58 35 L58 55" stroke="#4361ee" stroke-width="1" />
    <ellipse cx="50" cy="55" rx="8" ry="4" fill="#1e1e2c" stroke="#4361ee" stroke-width="1" />
  </g>
  
  <!-- 发光效果 -->
  <circle cx="100" cy="100" r="85" fill="none" stroke="url(#mainGradient)" stroke-width="1" opacity="0.3" stroke-dasharray="10,5" />
  <circle cx="100" cy="100" r="90" fill="none" stroke="url(#mainGradient)" stroke-width="1" opacity="0.2" stroke-dasharray="5,5" />
  
  <!-- 装饰性科技元素 -->
  <g stroke="url(#mainGradient)" stroke-width="1" opacity="0.4">
    <line x1="10" y1="100" x2="50" y2="100" />
    <line x1="150" y1="100" x2="190" y2="100" />
    <line x1="100" y1="10" x2="100" y2="50" />
    <line x1="100" y1="150" x2="100" y2="190" />
    
    <circle cx="100" cy="100" r="20" fill="none" />
    <circle cx="100" cy="100" r="30" fill="none" />
  </g>
</svg>