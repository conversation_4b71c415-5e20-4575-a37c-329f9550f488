:root {
  --primary: #4361ee;
  --primary-dark: #3a56d4;
  --secondary: #06d6a0;
  --dark: #1e1e2c;
  --darker: #161622;
  --light: #f8f9fa;
  --gray: #6c757d;
  --success: #06d6a0;
  --warning: #ffd166;
  --danger: #ef476f;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Segoe UI", "Noto Sans SC", Tahoma, Geneva, Verdana, sans-serif;
}

body {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--darker), var(--dark));
  color: var(--light);
  overflow-x: hidden;
}

/* 弹出窗口样式 */
.popup-body {
  width: 400px;
  min-height: 500px;
  padding: 20px;
}

/* 侧边栏样式 */
.sidepanel-body {
  width: 100%;
  min-height: 100vh;
  padding: 20px;
}

/* 头部样式 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, var(--primary), #5e35b1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
}

.logo-icon i {
  font-size: 18px;
  color: white;
}

.logo h1 {
  font-size: 22px;
  font-weight: 700;
  background: linear-gradient(to right, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.5px;
}

.status {
  background: rgba(255, 255, 255, 0.08);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--success);
}

/* 主内容区 */
.main-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
}

.card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--secondary);
}

.info-grid {
  display: grid;
  grid-template-columns: 120px 1fr;
  gap: 15px;
}

.info-label {
  font-weight: 600;
  opacity: 0.8;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #a0aec0;
}

.info-value {
  word-break: break-word;
  line-height: 1.5;
}

/* 截图区域 */
.screenshot-container {
  position: relative;
  margin-top: 15px;
  border-radius: 12px;
  overflow: hidden;
}

.screenshot {
  width: 100%;
  display: block;
  transition: transform 0.3s ease;
}

.screenshot:hover {
  transform: scale(1.02);
}

.screenshot-placeholder {
  height: 200px;
  background: linear-gradient(
    45deg,
    rgba(67, 97, 238, 0.1),
    rgba(6, 214, 160, 0.1)
  );
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  border: 2px dashed rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.screenshot-placeholder:hover {
  background: linear-gradient(
    45deg,
    rgba(67, 97, 238, 0.15),
    rgba(6, 214, 160, 0.15)
  );
  color: rgba(255, 255, 255, 0.7);
  border-color: var(--primary);
}

/* 图标区域 */
.logo-container {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-top: 10px;
}

.logo-preview {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.15);
  overflow: hidden;
  flex-shrink: 0;
}

.logo-preview img {
  width: 80%;
  height: 80%;
  object-fit: contain;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border-radius: 12px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
  font-size: 14px;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  flex: 1;
  box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark), #2f44b8);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 加载动画 */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 通知 */
.notification {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--success);
  color: white;
  padding: 12px 24px;
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  animation: fadeInOut 3s forwards;
  display: none;
  z-index: 1000;
  font-weight: 500;
}

@keyframes fadeInOut {
  0%,
  100% {
    opacity: 0;
    bottom: 0;
  }
  10%,
  90% {
    opacity: 1;
    bottom: 20px;
  }
}

/* 元数据区域 */
.metadata-container {
  max-height: 400px;
  overflow-y: auto;
  padding: 15px;
  background: rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  margin-top: 15px;
  font-family: "Fira Code", monospace;
  font-size: 13px;
  white-space: pre-wrap;
  line-height: 1.6;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* JSON 相关样式 */
.json-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.btn-small {
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.btn-small:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.btn-small.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.btn-small.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: var(--primary);
}

.json-container {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  overflow: hidden;
}

.json-content {
  padding: 20px;
  margin: 0;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #e8e8e8;
  background: transparent;
  overflow-x: auto;
  white-space: pre;
  max-height: 350px;
  overflow-y: auto;
}

/* JSON 语法高亮（简单版本） */
.json-content {
  color: #c9d1d9;
}

/* 滚动条样式 */
.json-content::-webkit-scrollbar,
.metadata-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.json-content::-webkit-scrollbar-track,
.metadata-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.json-content::-webkit-scrollbar-thumb,
.metadata-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.json-content::-webkit-scrollbar-thumb:hover,
.metadata-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 复制成功提示动画 */
.copy-success {
  background: var(--success) !important;
  color: white !important;
  animation: copyPulse 0.6s ease;
}

@keyframes copyPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 进度条 */
.progress-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  margin-top: 10px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  width: 0%;
  transition: width 0.3s ease;
}

/* 选项卡 */
.tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.tab {
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.08);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 13px;
}

.tab.active {
  background: var(--primary);
  box-shadow: 0 4px 10px rgba(67, 97, 238, 0.3);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* 图标网格 */
.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.icon-preview {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  overflow: hidden;
  padding: 6px;
}

.icon-preview img {
  max-width: 100%;
  max-height: 100%;
}

.icon-size {
  font-size: 11px;
  opacity: 0.7;
}

/* 页脚 */
.footer {
  margin-top: 20px;
  text-align: center;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 导航按钮 */
.nav-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.nav-btn {
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 13px;
  font-weight: 500;
}

.nav-btn:hover {
  background: var(--primary);
  transform: translateY(-1px);
}

.nav-btn.active {
  background: var(--primary);
}

/* 响应式调整 */
@media (max-width: 500px) {
  .popup-body {
    width: 100%;
    min-width: 320px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .icon-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .action-buttons {
    flex-direction: column;
  }

  .tabs {
    font-size: 12px;
  }

  .tab {
    padding: 6px 12px;
  }
}

/* 侧边栏特定样式 */
.sidepanel-body .info-grid {
  grid-template-columns: 140px 1fr;
}

.sidepanel-body .card {
  margin-bottom: 15px;
}

.sidepanel-body .screenshot-placeholder {
  height: 250px;
}

.sidepanel-body .icon-grid {
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
}

/* 按钮禁用状态 */
.btn-small:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-small:disabled:hover {
  background: rgba(255, 255, 255, 0.08) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

/* JSON容器阴影效果 */
.json-container {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(8px);
}

/* 代码选择样式 */
.json-content::selection {
  background: rgba(67, 97, 238, 0.3);
}

.json-content ::-moz-selection {
  background: rgba(67, 97, 238, 0.3);
}

/* JSON区域响应式调整 */
@media (max-width: 600px) {
  .json-header {
    padding: 10px 15px;
  }

  .json-actions {
    gap: 6px;
    top: 6px;
    right: 10px;
  }

  .btn-small {
    padding: 4px 8px;
  }

  .json-content {
    padding: 15px;
    font-size: 11px;
  }
}

/* 智能标签样式 */
.smart-tags {
  margin-top: 10px;
}

.tags-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--gray);
  font-size: 13px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
}

.smart-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: default;
  border: 1px solid transparent;
}

.smart-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 高置信度标签 */
.smart-tag.high-confidence {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border-color: #4CAF50;
}

/* 中等置信度标签 */
.smart-tag.medium-confidence {
  background: linear-gradient(135deg, #FF9800, #F57C00);
  color: white;
  border-color: #FF9800;
}

/* 低置信度标签 */
.smart-tag.low-confidence {
  background: linear-gradient(135deg, #9E9E9E, #757575);
  color: white;
  border-color: #9E9E9E;
}

/* 标签置信度指示器 */
.confidence-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
}

/* 标签分数显示 */
.tag-score {
  font-size: 10px;
  opacity: 0.8;
  margin-left: 2px;
}

/* 标签为空时的提示 */
.tags-empty {
  color: var(--gray);
  font-size: 12px;
  font-style: italic;
  padding: 8px 0;
}

/* 标签错误提示 */
.tags-error {
  color: var(--danger);
  font-size: 12px;
  padding: 8px 0;
}

/* 在info-grid中的标签显示 */
.info-value .tags-container {
  margin-top: 4px;
}

.info-value .smart-tag {
  font-size: 10px;
  padding: 2px 6px;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .smart-tag {
    font-size: 10px;
    padding: 3px 8px;
  }

  .tags-container {
    gap: 4px;
  }
}
